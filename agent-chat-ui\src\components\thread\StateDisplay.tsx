import { State } from "@/types/langgraph";

export function StateDisplay({ state }: { state: State }) {
  return (
    <div className="rounded-xl border border-gray-200 bg-white shadow-sm p-5 mb-4 max-w-2xl mx-auto">
      <h3 className="font-semibold text-lg text-gray-800 mb-3 flex items-center gap-2">
        {/* <span className="inline-block w-2 h-2 rounded-full bg-blue-500" /> */}
        Cart
      </h3>
      <div className="flex flex-col gap-2">
        <ul className="ml-6 mt-1 space-y-1 text-gray-700 text-sm">
          <li>
            <span className="font-medium text-gray-500">Country:</span>{" "}
            <span className="text-gray-900">{state?.offer?.country ?? "—"}</span>
          </li>
          <li>
            <span className="font-medium text-gray-500">Subscription:</span>{" "}
            <span className="text-gray-900">{state?.offer?.subscription ?? "—"}</span>
          </li>
          <li>
            <span className="font-medium text-gray-500">Term:</span>{" "}
            <span className="text-gray-900">{state?.offer?.term ?? "—"}</span>
          </li>
          <li>
            <span className="font-medium text-gray-500">Seats:</span>{" "}
            <span className="text-gray-900">{state?.offer?.seats ?? "—"}</span>
          </li>
          <li>
            <span className="font-medium text-gray-500">Price:</span>{" "}
            <span className="text-gray-900">
              {state?.offer.price !== undefined && state?.offer.price !== null && state?.offer.currency !== undefined && state?.offer.currency !== null
                ? `${state?.offer.currency} ${state?.offer.price.toFixed(2)}`
                : "—"}
            </span>
          </li>
        </ul>
      </div>
    </div>
  );
}