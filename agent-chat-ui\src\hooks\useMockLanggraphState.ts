import { useEffect, useState } from "react";
import { State } from "@/types/langgraph";

const MOCK_STATE: State = {
        offer: {country: "US",
        subscription: "Pro",
        term: "12 months",
        seats: 5,
        price: 99.99,
        currency: "EUR"
    },
};

export function useMockLanggraphState(threadId: string | null) {
    const [state, setState] = useState<State | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!threadId) {
            setState(null);
            return;
        }
        setLoading(true);
        // Simulate network delay
        const timeout = setTimeout(() => {
            setState(MOCK_STATE);
            setLoading(false);
        }, 500);
        return () => clearTimeout(timeout);
    }, [threadId]);

    return { state, loading };
}