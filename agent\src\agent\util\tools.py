from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState

from agent.util.states import State , Offer
from typing import Annotated

from datetime import datetime, timezone

@tool
def update_offer(
    params: Offer,
    state: Annotated[State, InjectedState],
):
    """This tool manages the user's current offer/shopping cart. Use this tool when the user wants to:
    - View their current offer/shopping cart
    - Update their offer parameters (country, subscription type, term length, number of users)
    - Modify any aspect of their current selection
    
    When updating, provide only the parameters the user wants to change. All other parameters 
    will remain unchanged from the previous offer. Available parameters:
    - country: User's country for pricing
    - subscription: Type of subscription/product
    - term: Billing period (monthly, 1-year, 3-year)
    - seats: Number of users/licenses
    - price: Total price of the offer (Use calculate_price tool to get this)
    - currency: Currency of the price (default is EUR)
    Use calculate_price tool to get the updated price after modifying any offer parameters.
    """
    offer = state.offer

    # Update only the parameters that are provided in `params`
    if params.country is not None:
        offer.country = params.country
    
    if params.subscription is not None:
        offer.subscription = params.subscription
    
    if params.term is not None:
        offer.term = params.term
    
    if params.seats is not None:
        offer.seats = params.seats
    
    if params.price is not None:
        offer.price = params.price
    else:
        offer.price = None

    if params.currency is not None:
        offer.currency = params.currency
    else:
        offer.currency = "EUR"
    
    
    state.offer = offer
    # Maybe wrap this in ToolMessage?
    return "Successfully updated parameters!"

@tool 
def update_price(
    price: float,
    currency: str,
    state: Annotated[State, InjectedState],
):
    """This is a tool to update the price and currency of the offer we are providing to the user."""
    offer = state.offer
    offer.price = price
    offer.currency = currency

    return "Successfully updated price and currency!"

@tool
def get_time():
    """This is a tool to get the current time. Format: YYYY-MM-DD HH:MM:SS"""
    now = datetime.now(timezone.utc)
    return f"The current time is {now.strftime('%Y-%m-%d %H:%M:%S')} UTC+0000."

TOOLS = [
    update_offer,
    get_time,
]
