apiVersion: v1
kind: Secret
metadata:
  name: api-secrets
  labels:
    app: sales-agent
    component: langgraph
    tier: api
    environment: production
stringData:
  CHAT_MODEL: azure_openai:gpt-4.1
  LANGSMITH_API_KEY: <your_langsmith_api_key>
  LANGSMITH_PROJECT: <your_langsmith_project_name>
  GOOGLE_API_KEY: <your_google_api_key>
  AZURE_OPENAI_API_KEY: <your_azure_openai_api_key>
  AZURE_OPENAI_ENDPOINT: "https://azureopenai-bi-dev-001.openai.azure.com/"
  OPENAI_API_VERSION: 2024-08-01-preview
  GALILEO_API_KEY: <your_galileo_api_key>
  GALILEO_PROJECT: sales-agent
  GALILEO_LOG_STREAM: dev
  GALILEO_CONSOLE_URL: https://app.galileo.ai
