apiVersion: apps/v1
kind: Deployment
metadata:
  name: langgraph-api
  labels:
    app: sales-agent
    component: langgraph
    tier: api
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: langgraph
      tier: api
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: langgraph
        tier: api
        environment: production
    spec:
      containers:
        - name: langgraph-api
          image: sales-agent-api:v0.0.1
          imagePullPolicy: Never
          env:
            - name: DATABASE_URI
              value: ****************************************************/postgres?sslmode=disable
            - name: REDIS_URI
              value: redis://langgraph-redis:6379/
          envFrom:
            - secretRef:
                name: api-secrets
          livenessProbe:
            httpGet:
              path: /ok
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /ok
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          ports:
            - containerPort: 8000
              protocol: TCP
