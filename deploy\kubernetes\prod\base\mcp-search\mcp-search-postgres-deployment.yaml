apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-search-postgres
  labels:
    app: sales-agent
    component: mcp-search
    tier: db
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: mcp-search
      tier: db
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: mcp-search
        tier: db
        environment: production
    spec:
      containers:
        - name: mcp-search-postgres
          image: pgvector/pgvector:pg17
          envFrom:
            - secretRef:
                name: mcp-search-postgres-credentials
          livenessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          ports:
            - containerPort: 5432
              protocol: TCP
          volumeMounts:
            - name: init-sql
              mountPath: /docker-entrypoint-initdb.d/init-pgvector.sql # this only runs if the database is empty
              subPath: init-pgvector.sql
            - mountPath: /var/lib/postgresql/data
              name: mcp-search-data
      volumes:
        - name: init-sql
          configMap:
            name: pgvector-init-sql
        - name: mcp-search-data
          persistentVolumeClaim:
            claimName: mcp-search-data
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pgvector-init-sql
data:
  init-pgvector.sql: |
    CREATE EXTENSION IF NOT EXISTS vector;
