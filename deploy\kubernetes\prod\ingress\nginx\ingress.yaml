apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  rules:
  - host: "az01vm00000009.graphisoft.hu"
    http:
      paths:
      - path: /api(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: langgraph-api
            port:
              number: 8123
      - path: /pricing(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: mcp-pricing
            port:
              number: 8000
      - path: /search(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: mcp-search
            port:
              number: 8000
      - path: /()(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: agent-chat-ui
            port:
              number: 3000
