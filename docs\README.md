# Sales Agent Documentation

Welcome to the Sales Agent project documentation. This comprehensive guide covers everything you need to know about building, deploying, and maintaining the AI-powered sales agent system.

## Quick Links

- **[🚀 Quick Start](quick-start.html)** - Get up and running in minutes
- **[🏗️ Architecture](architecture.html)** - Understand the system design
- **[🔧 Services](services.html)** - Detailed service documentation
- **[🚀 Deployment](deployment.html)** - Production deployment guide
- **[💻 Development](development.html)** - Development environment setup

## About This Project

The Sales Agent is a sophisticated AI-powered system designed to assist with sales operations through natural language interactions. Built with modern technologies like LangGraph, Next.js, and various AI services, it provides:

- **Intelligent Conversations**: Natural language processing for complex sales queries
- **Product Information**: Comprehensive product catalogs and documentation
- **Pricing Calculations**: Real-time pricing for multiple software vendors
- **Semantic Search**: Advanced search capabilities across product documentation
- **Multi-vendor Support**: Integration with Allplan, Graphisoft, Vectorworks, and more

## Technology Stack

- **Agent Core**: LangGraph for AI orchestration
- **Frontend**: Next.js with React and TypeScript
- **Backend Services**: Python with FastAPI/MCP servers
- **Databases**: PostgreSQL and TimescaleDB for vector storage
- **Deployment**: Docker and Kubernetes
- **Monitoring**: Prometheus and Grafana

## Getting Started

Choose your path:

### 🏃‍♂️ I Want to Try It Now
Follow the [Quick Start guide](quick-start.html) to get a demo running locally in under 10 minutes.

### 🔧 I Want to Develop
Check out the [Development guide](development.html) for setting up a complete development environment.

### 🚀 I Want to Deploy to Production
See the [Deployment guide](deployment.html) for Kubernetes and cloud deployment instructions.

### 🧠 I Want to Understand the Architecture
Read the [Architecture overview](architecture.html) to understand how everything fits together.

## Project Structure

```
sales-agent/
├── agent/                 # LangGraph AI agent
├── agent-chat-ui/         # Next.js frontend
├── pricing/               # Pricing service (MCP server)
├── search/                # Search service with vector store
├── scraping/              # Web scraping tools
├── scrapedDocsToVectorStore/ # Document processing
├── deploy/                # Deployment configurations
├── docs/                  # This documentation site
└── scripts/               # Automation scripts
```

## Contributing

We welcome contributions! Please see our [Development guide](development.html) for:

- Setting up the development environment
- Code style guidelines
- Testing practices
- Pull request process

## Support

- **GitHub Issues**: [Report bugs or request features](https://github.com/Nemetschek-SE/sales-agent/issues)
- **Documentation**: Browse this site for comprehensive guides
- **Development**: Check the troubleshooting sections in each guide

---

*This documentation is built with [Jekyll](https://jekyllrb.com) and the [Just the Docs](https://just-the-docs.github.io/just-the-docs/) theme.*