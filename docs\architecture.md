---
title: Architecture
layout: default
nav_order: 2
---

# System Architecture

The Sales Agent system is built as a microservices architecture with several interconnected components that work together to provide an AI-powered sales experience.

## Overview

The system consists of the following main components:

### High-Level Architecture

```mermaid
graph TB
    User[👤 User] --> UI[🎨 Agent Chat UI<br/>Next.js Frontend]
    UI --> Agent[🤖 LangGraph Agent<br/>Core AI Logic]
    
    Agent --> MCP1[💰 Pricing Service<br/>MCP Server]
    Agent --> MCP2[🔍 Search Service<br/>MCP Server]
    
    MCP1 --> PDB[(🗄️ PostgreSQL<br/>Pricing Data)]
    MCP2 --> VDB[(📊 TimescaleDB<br/>Vector Store)]
    
    Scraping[🕷️ Web Scraping<br/>Scrapy] --> Processing[📚 Document Processing<br/>Vector Store Ingestion]
    Processing --> VDB
    
    MCP1 -.->|"MCP Protocol"| Agent
    MCP2 -.->|"MCP Protocol"| Agent
    
    subgraph "Data Pipeline"
        Scraping
        Processing
    end
    
    subgraph "Core Services"
        Agent
        MCP1
        MCP2
    end
    
    subgraph "Data Layer"
        PDB
        VDB
    end
```

### Service Communication Flow

```mermaid
sequenceDiagram
    participant User
    participant U<PERSON> as Chat UI
    participant Agent as LangGraph Agent
    participant Pricing as Pricing MCP
    participant Search as Search MCP
    participant PDB as PostgreSQL
    participant VDB as TimescaleDB

    User->>UI: "What's the price of Archicad in Germany?"
    UI->>Agent: Process Query
    
    Agent->>Agent: Analyze Query Intent
    
    par Pricing Query
        Agent->>Pricing: calculate_price(Archicad, DE, ...)
        Pricing->>PDB: Query pricing data
        PDB-->>Pricing: Return prices
        Pricing-->>Agent: Pricing information
    and Product Info Query
        Agent->>Search: search_documents("Archicad features")
        Search->>VDB: Vector similarity search
        VDB-->>Search: Relevant documents
        Search-->>Agent: Product information
    end
    
    Agent->>Agent: Synthesize Response
    Agent-->>UI: Complete Response
    UI-->>User: Display Results
```

## Component Details

### 🤖 LangGraph Agent
- **Technology**: LangGraph, Python
- **Purpose**: Core AI agent that orchestrates conversations and decision-making
- **Features**:
  - Multi-step reasoning and planning
  - Tool integration for pricing and search
  - State management for conversation context
  - Visual debugging through LangGraph Studio

### 🎨 Chat UI
- **Technology**: Next.js, React, TypeScript
- **Purpose**: User interface for interacting with the sales agent
- **Features**:
  - Real-time chat interface
  - File upload capabilities
  - Thread management
  - Responsive design with Tailwind CSS

### 💰 Pricing Service
- **Technology**: Python, PostgreSQL, MCP (Model Context Protocol)
- **Purpose**: Handles product pricing calculations and queries
- **Features**:
  - Multi-provider pricing data (Allplan, Graphisoft, Vectorworks, etc.)
  - Country-specific pricing
  - Currency conversion
  - RESTful API endpoints

### 🔍 Search Service
- **Technology**: Python, TimescaleDB, Vector Embeddings
- **Purpose**: Semantic search and report generation
- **Features**:
  - Multi-LLM integration (Azure OpenAI, Google AI)
  - Advanced query rephrasing
  - Vector similarity search
  - AI-powered report generation
  - Reference-aware documentation

### 🕷️ Web Scraping
- **Technology**: Scrapy, MongoDB
- **Purpose**: Collects product information from various websites
- **Features**:
  - Configurable spider framework
  - Structured data extraction
  - Rate limiting and respectful crawling

### 📚 Document Processing
- **Technology**: Python, Vector Embeddings
- **Purpose**: Processes scraped content for vector storage
- **Features**:
  - Text chunking and preprocessing
  - Embedding generation
  - Vector store ingestion
  - Content deduplication

## Data Flow

### 1. Document Ingestion Pipeline

```mermaid
flowchart LR
    subgraph "Data Sources"
        W1[🌐 Graphisoft Website]
        W2[🌐 Allplan Website]
        W3[🌐 Vectorworks Website]
        W4[🌐 Other Vendors]
    end
    
    subgraph "Scraping Layer"
        S1[📄 Scrapy Spider 1]
        S2[📄 Scrapy Spider 2]
        S3[📄 Scrapy Spider 3]
        S4[📄 Scrapy Spider N]
    end
    
    subgraph "Processing Layer"
        P1[🔧 Text Extraction]
        P2[🔧 Content Cleaning]
        P3[🔧 Chunking]
        P4[🔧 Embedding Generation]
    end
    
    subgraph "Storage Layer"
        MongoDB[(🗃️ MongoDB<br/>Raw Data)]
        VectorDB[(📊 TimescaleDB<br/>Vector Store)]
    end
    
    W1 --> S1
    W2 --> S2
    W3 --> S3
    W4 --> S4
    
    S1 --> MongoDB
    S2 --> MongoDB
    S3 --> MongoDB
    S4 --> MongoDB
    
    MongoDB --> P1
    P1 --> P2
    P2 --> P3
    P3 --> P4
    P4 --> VectorDB
```

### 2. User Query Processing

```mermaid
flowchart TD
    Q[❓ User Query] --> A[🤖 LangGraph Agent]
    
    A --> D{🧠 Query Analysis}
    
    D -->|Pricing Query| T1[💰 Pricing Tool]
    D -->|Product Info| T2[🔍 Search Tool]
    D -->|General Chat| T3[💬 Chat Tool]
    
    T1 --> MCP1[💰 Pricing MCP]
    T2 --> MCP2[🔍 Search MCP]
    
    MCP1 --> DB1[(🗄️ PostgreSQL)]
    MCP2 --> DB2[(📊 TimescaleDB)]
    
    DB1 --> R1[📊 Pricing Results]
    DB2 --> R2[📄 Search Results]
    
    R1 --> A
    R2 --> A
    T3 --> A
    
    A --> S[🎯 Response Synthesis]
    S --> R[📝 Final Response]
```

### 3. MCP (Model Context Protocol) Integration

```mermaid
graph LR
    subgraph "LangGraph Agent"
        A[🤖 Agent Core]
        TM[🛠️ Tool Manager]
    end
    
    subgraph "MCP Servers"
        MCP1[💰 Pricing MCP<br/>FastMCP]
        MCP2[🔍 Search MCP<br/>FastMCP]
    end
    
    subgraph "Tools Exposed via MCP"
        T1[calculate_price]
        T2[get_providers]
        T3[search_documents]
        T4[generate_report]
    end
    
    A <--> TM
    TM <-->|"MCP Protocol<br/>HTTP/WebSocket"| MCP1
    TM <-->|"MCP Protocol<br/>HTTP/WebSocket"| MCP2
    
    MCP1 --> T1
    MCP1 --> T2
    MCP2 --> T3
    MCP2 --> T4
```

## Detailed Component Architecture

### 1. Core Services Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[🖥️ Next.js Chat UI<br/>Port: 3000]
    end
    
    subgraph "Agent Layer"
        AG[🤖 LangGraph Agent<br/>Python/FastAPI]
        
        subgraph "Agent Components"
            GR[📊 Graph Engine]
            ST[🧠 State Manager]
            TL[🛠️ Tool Loader]
            MC[📡 MCP Client]
        end
        
        AG --> GR
        AG --> ST
        AG --> TL
        AG --> MC
    end
    
    subgraph "MCP Services"
        subgraph "Pricing Service"
            PM[💰 Pricing MCP<br/>Port: 8001]
            PC[🧮 Price Calculator]
            PP[📊 Pricing Provider]
        end
        
        subgraph "Search Service"
            SM[🔍 Search MCP<br/>Port: 8002]
            VS[📊 Vector Search]
            VE[🧮 Vector Engine]
        end
    end
    
    subgraph "Data Layer"
        subgraph "Databases"
            PG[(🗄️ PostgreSQL<br/>Pricing Data)]
            TS[(📊 TimescaleDB<br/>Vector Store)]
            MG[(🗃️ MongoDB<br/>Raw Documents)]
        end
    end
    
    subgraph "Ingestion Layer"
        SC[🕷️ Scrapy<br/>Web Scraping]
        VI[🔧 Vector Ingestion<br/>Document Processing]
    end
    
    UI -->|HTTP/WebSocket| AG
    MC -->|MCP Protocol| PM
    MC -->|MCP Protocol| SM
    
    PM --> PC
    PC --> PP
    PP --> PG
    
    SM --> VS
    VS --> VE
    VE --> TS
    
    SC --> MG
    VI --> MG
    VI --> TS
```

### 2. Technology Stack Detail

```mermaid
mindmap
  root)Sales Agent Tech Stack(
    Frontend
      Next.js 14
      React
      TypeScript
      Tailwind CSS
      ShadCN/UI
    Backend
      Python 3.11+
      LangGraph
      FastAPI
      FastMCP
    AI & ML
      LangChain
      OpenAI GPT
      Embeddings
      Vector Search
    Data Storage
      PostgreSQL
        Pricing Data
        Configuration
      TimescaleDB
        Vector Store
        Embeddings
      MongoDB
        Raw Documents
        Scraped Data
    Infrastructure
      Docker
      Kubernetes
      GitHub Actions
      Jekyll Docs
    Protocols
      MCP
        Model Context Protocol
        Tool Integration
      REST APIs
      WebSockets
      GraphQL
```

## Deployment Architecture

### Kubernetes Production Deployment

```mermaid
graph TB
    subgraph "Kubernetes Cluster"
        subgraph "Namespace: sales-agent-prod"
            subgraph "Frontend Pods"
                FP1[🖥️ UI Pod 1]
                FP2[🖥️ UI Pod 2]
                FS[🔧 UI Service]
            end
            
            subgraph "Agent Pods"
                AP1[🤖 Agent Pod 1]
                AP2[🤖 Agent Pod 2]
                AS[🔧 Agent Service]
            end
            
            subgraph "MCP Service Pods"
                MP1[💰 Pricing Pod 1]
                MP2[💰 Pricing Pod 2]
                MS1[🔧 Pricing Service]
                SP1[🔍 Search Pod 1]
                SP2[🔍 Search Pod 2]
                MS2[🔧 Search Service]
            end
        end
        
        subgraph "Shared Services"
            PGB[🗄️ PostgreSQL<br/>StatefulSet]
            TSB[📊 TimescaleDB<br/>StatefulSet]
            MGB[🗃️ MongoDB<br/>StatefulSet]
            
            PVC1[💾 PostgreSQL PVC]
            PVC2[💾 TimescaleDB PVC]
            PVC3[💾 MongoDB PVC]
        end
        
        subgraph "Ingress & Networking"
            ING[🌐 Ingress Controller]
            LB[⚖️ Load Balancer]
            CM[🔧 ConfigMaps]
            SEC[🔐 Secrets]
        end
    end
    
    subgraph "External"
        USER[👤 Users]
        DNS[🌐 DNS]
        REG[📦 Container Registry]
    end
    
    USER --> DNS
    DNS --> LB
    LB --> ING
    ING --> FS
    
    FS --> FP1
    FS --> FP2
    
    FP1 --> AS
    FP2 --> AS
    
    AS --> AP1
    AS --> AP2
    
    AP1 --> MS1
    AP1 --> MS2
    AP2 --> MS1
    AP2 --> MS2
    
    MS1 --> MP1
    MS1 --> MP2
    MS2 --> SP1
    MS2 --> SP2
    
    MP1 --> PGB
    MP2 --> PGB
    SP1 --> TSB
    SP2 --> TSB
    
    PGB --> PVC1
    TSB --> PVC2
    MGB --> PVC3
    
    CM --> AP1
    CM --> AP2
    CM --> MP1
    CM --> MP2
    CM --> SP1
    CM --> SP2
    
    SEC --> AP1
    SEC --> MP1
    SEC --> SP1
    
    REG -.-> FP1
    REG -.-> AP1
    REG -.-> MP1
    REG -.-> SP1
```

### Development Environment
- Local Docker Compose setup
- Individual service containers
- Shared volumes and networks

### Production Environment
- Kubernetes deployment
- Separate namespaces for dev/prod
- Load balancing and auto-scaling
- Persistent storage for databases

### CI/CD Pipeline
- GitHub Actions workflows
- Automated testing (unit, integration)
- Docker image building
- Kubernetes deployment automation

## Security Considerations

- **API Authentication**: LangSmith API keys for production deployments
- **Database Security**: Encrypted connections and access controls
- **Environment Variables**: Secure secret management
- **Network Isolation**: Service-to-service communication controls

## Scalability Features

- **Horizontal Scaling**: Stateless service design
- **Caching**: Vector search result caching
- **Load Balancing**: Request distribution across instances
- **Database Optimization**: Indexed vector searches and optimized queries
