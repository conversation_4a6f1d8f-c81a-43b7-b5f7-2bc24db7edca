---
title: Deployment
layout: default
nav_order: 5
---

# Deployment Guide

This guide covers deployment options for the Sales Agent system, from local development to production Kubernetes clusters.

## Deployment Options

### 1. Docker Compose (Development/Testing)
### 2. Kubernetes (Production)
### 3. Cloud Platforms (AWS, Azure, GCP)

## Docker Compose Deployment

### Prerequisites

- Docker 24.0+
- Docker Compose v2.0+
- 8GB+ RAM (for all services)
- 20GB+ disk space

### Quick Start

```bash
# Clone repository
git clone https://github.com/Nemetschek-SE/sales-agent.git
cd sales-agent

# Create environment files
cp .env.example .env
cp agent/.env.example agent/.env
cp pricing/.env.example pricing/.env
cp search/.env.example search/.env

# Configure API keys in .env files
# Edit the files with your actual API keys

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### Service Configuration

Create a main `docker-compose.yml`:

```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: sales_agent
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-READY", "pg_isready", "-U", "postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TimescaleDB for vector storage
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      POSTGRES_DB: vector_store
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - timescale_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # Agent Service
  agent:
    build: ./agent
    ports:
      - "8123:8123"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY}
      - PRICING_SERVICE_URL=http://pricing:8001
      - SEARCH_SERVICE_URL=http://search:8002
    depends_on:
      - postgres
      - pricing
      - search
    volumes:
      - ./agent:/app
    restart: unless-stopped

  # Pricing Service
  pricing:
    build: ./pricing
    ports:
      - "8001:8001"
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=sales_agent
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

  # Search Service
  search:
    build: ./search
    ports:
      - "8002:8002"
    environment:
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - TIMESCALE_HOST=timescaledb
      - TIMESCALE_PORT=5432
      - TIMESCALE_DB=vector_store
      - TIMESCALE_USER=postgres
      - TIMESCALE_PASSWORD=password
    depends_on:
      - timescaledb
    restart: unless-stopped

  # Chat UI
  chat-ui:
    build: ./agent-chat-ui
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_LANGGRAPH_URL=http://localhost:8123
      - NEXT_PUBLIC_ASSISTANT_ID=agent
    depends_on:
      - agent
    restart: unless-stopped

volumes:
  postgres_data:
  timescale_data:
  redis_data:

networks:
  default:
    name: sales-agent-network
```

### Environment Configuration

#### Main `.env` file:
```bash
# API Keys
OPENAI_API_KEY=sk-your-openai-key
LANGSMITH_API_KEY=your-langsmith-key
AZURE_OPENAI_API_KEY=your-azure-key
GOOGLE_AI_API_KEY=your-google-key

# Database passwords
POSTGRES_PASSWORD=secure_password_here
REDIS_PASSWORD=redis_password_here

# Application settings
ENVIRONMENT=production
LOG_LEVEL=INFO
```

## Kubernetes Deployment

### Prerequisites

- Kubernetes cluster (1.24+)
- kubectl configured
- Helm 3.0+
- Ingress controller (nginx recommended)

### Namespace Setup

```bash
# Create namespace
kubectl create namespace sales-agent

# Set as default
kubectl config set-context --current --namespace=sales-agent
```

### Database Setup

#### PostgreSQL with Helm

```bash
# Add Bitnami repository
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update

# Install PostgreSQL
helm install postgres bitnami/postgresql \
  --set auth.postgresPassword=secure_password \
  --set auth.database=sales_agent \
  --set primary.persistence.size=50Gi \
  --set primary.resources.requests.memory=2Gi \
  --set primary.resources.requests.cpu=1000m
```

#### TimescaleDB

```yaml
# timescaledb.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: timescaledb
spec:
  serviceName: timescaledb
  replicas: 1
  selector:
    matchLabels:
      app: timescaledb
  template:
    metadata:
      labels:
        app: timescaledb
    spec:
      containers:
      - name: timescaledb
        image: timescale/timescaledb:latest-pg15
        env:
        - name: POSTGRES_DB
          value: vector_store
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: timescale-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: data
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
---
apiVersion: v1
kind: Service
metadata:
  name: timescaledb
spec:
  selector:
    app: timescaledb
  ports:
  - port: 5432
    targetPort: 5432
```

### Application Deployments

#### Agent Service

```yaml
# agent-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agent
  template:
    metadata:
      labels:
        app: agent
    spec:
      containers:
      - name: agent
        image: sales-agent/agent:latest
        ports:
        - containerPort: 8123
        env:
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: openai-key
        - name: PRICING_SERVICE_URL
          value: "http://pricing:8001"
        - name: SEARCH_SERVICE_URL
          value: "http://search:8002"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi" 
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8123
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8123
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: agent
spec:
  selector:
    app: agent
  ports:
  - port: 8123
    targetPort: 8123
```

#### Pricing Service

```yaml
# pricing-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pricing
spec:
  replicas: 2
  selector:
    matchLabels:
      app: pricing
  template:
    metadata:
      labels:
        app: pricing
    spec:
      containers:
      - name: pricing
        image: sales-agent/pricing:latest
        ports:
        - containerPort: 8001
        env:
        - name: POSTGRES_HOST
          value: postgres-postgresql
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: pricing
spec:
  selector:
    app: pricing
  ports:
  - port: 8001
    targetPort: 8001
```

#### Search Service

```yaml
# search-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: search
spec:
  replicas: 2
  selector:
    matchLabels:
      app: search
  template:
    metadata:
      labels:
        app: search
    spec:
      containers:
      - name: search
        image: sales-agent/search:latest
        ports:
        - containerPort: 8002
        env:
        - name: AZURE_OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: azure-openai-key
        - name: TIMESCALE_HOST
          value: timescaledb
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: search
spec:
  selector:
    app: search
  ports:
  - port: 8002
    targetPort: 8002
```

#### Chat UI

```yaml
# chat-ui-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chat-ui
spec:
  replicas: 3
  selector:
    matchLabels:
      app: chat-ui
  template:
    metadata:
      labels:
        app: chat-ui
    spec:
      containers:
      - name: chat-ui
        image: sales-agent/chat-ui:latest
        ports:
        - containerPort: 3000
        env:
        - name: NEXT_PUBLIC_LANGGRAPH_URL
          value: "https://sales-agent.your-domain.com/api"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: chat-ui
spec:
  selector:
    app: chat-ui
  ports:
  - port: 3000
    targetPort: 3000
```

### Ingress Configuration

```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sales-agent-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - sales-agent.your-domain.com
    secretName: sales-agent-tls
  rules:
  - host: sales-agent.your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: chat-ui
            port:
              number: 3000
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: agent
            port:
              number: 8123
```

### Secrets Management

```yaml
# secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: api-keys
type: Opaque
data:
  openai-key: <base64-encoded-key>
  azure-openai-key: <base64-encoded-key>
  google-ai-key: <base64-encoded-key>
  langsmith-key: <base64-encoded-key>
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
type: Opaque
data:
  password: <base64-encoded-password>
---
apiVersion: v1
kind: Secret
metadata:
  name: timescale-secret
type: Opaque
data:
  password: <base64-encoded-password>
```

### Deployment Commands

```bash
# Create secrets (encode your keys first)
echo -n "your-api-key" | base64
kubectl apply -f secrets.yaml

# Deploy databases
kubectl apply -f timescaledb.yaml
helm install postgres bitnami/postgresql # (as shown above)

# Deploy applications
kubectl apply -f agent-deployment.yaml
kubectl apply -f pricing-deployment.yaml
kubectl apply -f search-deployment.yaml
kubectl apply -f chat-ui-deployment.yaml

# Set up ingress
kubectl apply -f ingress.yaml

# Check deployment status
kubectl get pods
kubectl get services
kubectl get ingress
```

## Monitoring and Observability

### Prometheus & Grafana

```yaml
# monitoring.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'kubernetes-pods'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
      volumes:
      - name: config
        configMap:
          name: prometheus-config
```

### Logging with ELK Stack

```yaml
# logging.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
spec:
  selector:
    matchLabels:
      name: fluent-bit
  template:
    metadata:
      labels:
        name: fluent-bit
    spec:
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:latest
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
```

## Scaling and Performance

### Horizontal Pod Autoscaler

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Database Optimization

```sql
-- Performance tuning for PostgreSQL
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
SELECT pg_reload_conf();

-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_pricing_product_country 
ON pricing(product_id, country_id);

CREATE INDEX CONCURRENTLY idx_vector_embeddings 
ON documents USING ivfflat (embedding vector_cosine_ops);
```

## Security

### Network Policies

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: sales-agent-network-policy
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  - from:
    - podSelector: {}
  egress:
  - to:
    - podSelector: {}
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

### Pod Security Standards

```yaml
# pod-security.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: sales-agent
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
```

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# backup-script.sh

# PostgreSQL backup
kubectl exec -n sales-agent postgres-postgresql-0 -- \
  pg_dump -U postgres sales_agent > backup-$(date +%Y%m%d).sql

# TimescaleDB backup
kubectl exec -n sales-agent timescaledb-0 -- \
  pg_dump -U postgres vector_store > vector-backup-$(date +%Y%m%d).sql

# Upload to S3 (optional)
aws s3 cp backup-$(date +%Y%m%d).sql s3://your-backup-bucket/
```

### Disaster Recovery

```yaml
# velero-backup.yaml
apiVersion: velero.io/v1
kind: Backup
metadata:
  name: sales-agent-backup
spec:
  includedNamespaces:
  - sales-agent
  storageLocation: default
  ttl: 720h0m0s
```

## CI/CD Pipeline

### GitHub Actions

```yaml
# .github/workflows/deploy.yml
name: Deploy to Kubernetes

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push images
      run: |
        docker build -t ghcr.io/nemetschek-se/sales-agent/agent:${{ github.sha }} ./agent
        docker build -t ghcr.io/nemetschek-se/sales-agent/pricing:${{ github.sha }} ./pricing
        docker build -t ghcr.io/nemetschek-se/sales-agent/search:${{ github.sha }} ./search
        docker build -t ghcr.io/nemetschek-se/sales-agent/chat-ui:${{ github.sha }} ./agent-chat-ui
        
        docker push ghcr.io/nemetschek-se/sales-agent/agent:${{ github.sha }}
        docker push ghcr.io/nemetschek-se/sales-agent/pricing:${{ github.sha }}
        docker push ghcr.io/nemetschek-se/sales-agent/search:${{ github.sha }}
        docker push ghcr.io/nemetschek-se/sales-agent/chat-ui:${{ github.sha }}
    
    - name: Deploy to Kubernetes
      run: |
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
        export KUBECONFIG=kubeconfig
        
        kubectl set image deployment/agent agent=ghcr.io/nemetschek-se/sales-agent/agent:${{ github.sha }}
        kubectl set image deployment/pricing pricing=ghcr.io/nemetschek-se/sales-agent/pricing:${{ github.sha }}
        kubectl set image deployment/search search=ghcr.io/nemetschek-se/sales-agent/search:${{ github.sha }}
        kubectl set image deployment/chat-ui chat-ui=ghcr.io/nemetschek-se/sales-agent/chat-ui:${{ github.sha }}
        
        kubectl rollout status deployment/agent
        kubectl rollout status deployment/pricing  
        kubectl rollout status deployment/search
        kubectl rollout status deployment/chat-ui
```

## Troubleshooting

### Common Issues

**Pods not starting**
```bash
# Check pod status
kubectl get pods
kubectl describe pod <pod-name>
kubectl logs <pod-name>

# Check events
kubectl get events --sort-by=.metadata.creationTimestamp
```

**Database connection issues**
```bash
# Test database connectivity
kubectl exec -it postgres-postgresql-0 -- psql -U postgres

# Check service discovery
kubectl exec -it agent-pod -- nslookup postgres-postgresql
```

**Resource constraints**
```bash
# Check resource usage
kubectl top pods
kubectl top nodes

# Check resource requests/limits
kubectl describe pod <pod-name>
```

### Performance Monitoring

```bash
# Monitor service performance
kubectl exec -it agent-pod -- curl http://localhost:8123/metrics

# Check database performance
kubectl exec -it postgres-postgresql-0 -- \
  psql -U postgres -c "SELECT * FROM pg_stat_activity;"
```
