---
title: Development
layout: default
nav_order: 6
---

# Development Guide

This guide covers setting up a development environment, coding standards, testing practices, and contribution guidelines for the Sales Agent project.

## Development Environment Setup

### Prerequisites

Ensure you have the following tools installed:

- **Git**: Version control
- **Docker & Docker Compose**: For containerized development
- **Python 3.9+**: Core language for backend services
- **Node.js 18+**: For the Chat UI
- **pnpm**: Package manager for Node.js projects
- **PostgreSQL**: Database (can be run via Docker)
- **VS Code**: Recommended editor with extensions

### Recommended VS Code Extensions

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-kubernetes-tools.vscode-kubernetes-tools",
    "ms-vscode-remote.remote-containers"
  ]
}
```

### Initial Setup

1. **Clone the Repository**

```bash
git clone https://github.com/Nemetschek-SE/sales-agent.git
cd sales-agent
```

2. **Set Up Development Environment**

```bash
# Create and configure environment files
make setup-dev

# Or manually:
cp .env.example .env
cp agent/.env.example agent/.env
cp pricing/.env.example pricing/.env
cp search/.env.example search/.env
```

3. **Start Development Services**

```bash
# Start all services with hot reload
docker-compose -f docker-compose.dev.yml up -d

# Or start services individually (see service-specific guides)
```

### Development Docker Compose

Create `docker-compose.dev.yml` for development:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: sales_agent_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql

  timescaledb:
    image: timescale/timescaledb:latest-pg15
    environment:
      POSTGRES_DB: vector_store_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5433:5432"
    volumes:
      - timescale_dev_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  postgres_dev_data:
  timescale_dev_data:
  redis_dev_data:
```

## Service Development

### Agent Service Development

```bash
cd agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install in development mode
pip install -e .
pip install -r requirements-dev.txt

# Start with hot reload
langgraph dev --watch
```

#### Development Dependencies

```txt
# requirements-dev.txt
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.0.0
pre-commit>=3.0.0
```

### Chat UI Development

```bash
cd agent-chat-ui

# Install dependencies
pnpm install

# Start development server with hot reload
pnpm dev

# Type checking
pnpm type-check

# Linting
pnpm lint
pnpm lint:fix
```

### Pricing Service Development

```bash
cd pricing

# Setup virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Start with auto-reload
uvicorn src.mcp_server:app --reload --host 0.0.0.0 --port 8001
```

### Search Service Development

```bash
cd search

# Setup environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Start development server
python src/mcp_server.py --debug
```

## Code Style and Standards

### Python Standards

We follow PEP 8 with some modifications. Configuration is in `pyproject.toml`:

```toml
[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["agent", "pricing", "search"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "venv",
    "build",
    "dist"
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

### TypeScript/JavaScript Standards

Configuration in `eslint.config.js` and `prettier.config.js`:

```javascript
// eslint.config.js
import { defineConfig } from 'eslint-define-config';

export default defineConfig({
  extends: [
    '@next/eslint-config-next',
    'prettier'
  ],
  rules: {
    'prefer-const': 'error',
    'no-unused-vars': 'error',
    '@typescript-eslint/no-unused-vars': 'error'
  }
});

// prettier.config.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 100,
  tabWidth: 2
};
```

### Pre-commit Hooks

Set up pre-commit hooks to ensure code quality:

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        files: ^(agent|pricing|search)/

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^(agent|pricing|search)/

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        files: ^(agent|pricing|search)/
```

Install pre-commit:

```bash
pip install pre-commit
pre-commit install
```

## Testing

### Python Testing

#### Unit Tests

```python
# tests/unit/test_pricing_calculator.py
import pytest
from pricing.pricing_calculator import PricingCalculator

class TestPricingCalculator:
    @pytest.fixture
    def calculator(self):
        return PricingCalculator()
    
    def test_calculate_price_basic(self, calculator):
        """Test basic price calculation."""
        result = calculator.calculate_price(
            product_id=1,
            country="DE",
            license_type="commercial"
        )
        assert result["price"] > 0
        assert result["currency"] == "EUR"
    
    def test_calculate_price_invalid_product(self, calculator):
        """Test price calculation with invalid product."""
        with pytest.raises(ValueError):
            calculator.calculate_price(
                product_id=999999,
                country="DE"
            )
```

#### Integration Tests

```python
# tests/integration/test_agent_flow.py
import pytest
from agent.graph import create_graph

class TestAgentFlow:
    @pytest.fixture
    def graph(self):
        return create_graph()
    
    @pytest.mark.asyncio
    async def test_pricing_query_flow(self, graph):
        """Test end-to-end pricing query flow."""
        input_data = {
            "messages": [
                {"role": "user", "content": "What is the price of Archicad in Germany?"}
            ]
        }
        
        result = await graph.ainvoke(input_data)
        
        assert "messages" in result
        assert len(result["messages"]) > 1
        assert "price" in result["messages"][-1]["content"].lower()
```

#### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src tests/

# Run specific test file
pytest tests/unit/test_pricing_calculator.py

# Run tests with verbose output
pytest -v

# Run tests and generate HTML coverage report
pytest --cov=src --cov-report=html tests/
```

### JavaScript/TypeScript Testing

#### Jest Configuration

```javascript
// jest.config.js
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
  ],
};

module.exports = createJestConfig(customJestConfig);
```

#### Component Tests

```typescript
// __tests__/components/MessageInput.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { MessageInput } from '@/components/MessageInput';

describe('MessageInput', () => {
  it('renders input field', () => {
    render(<MessageInput onSendMessage={jest.fn()} />);
    expect(screen.getByPlaceholderText(/type a message/i)).toBeInTheDocument();
  });

  it('calls onSendMessage when form is submitted', () => {
    const mockSendMessage = jest.fn();
    render(<MessageInput onSendMessage={mockSendMessage} />);
    
    const input = screen.getByPlaceholderText(/type a message/i);
    const button = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(button);
    
    expect(mockSendMessage).toHaveBeenCalledWith('Test message', []);
  });
});
```

## Database Development

### Migrations

#### PostgreSQL Migrations

```python
# migrations/001_initial_schema.py
from typing import List
from database import Database

def up(db: Database) -> None:
    """Apply migration."""
    queries = [
        """
        CREATE TABLE IF NOT EXISTS providers (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS products (
            id SERIAL PRIMARY KEY,
            provider_id INTEGER REFERENCES providers(id),
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
    ]
    
    for query in queries:
        db.execute(query)

def down(db: Database) -> None:
    """Rollback migration."""
    db.execute("DROP TABLE IF EXISTS products;")
    db.execute("DROP TABLE IF EXISTS providers;")
```

#### Migration Runner

```python
# scripts/migrate.py
import os
import sys
from pathlib import Path

def run_migrations():
    """Run database migrations."""
    migrations_dir = Path("migrations")
    applied_migrations = get_applied_migrations()
    
    for migration_file in sorted(migrations_dir.glob("*.py")):
        migration_name = migration_file.stem
        
        if migration_name not in applied_migrations:
            print(f"Applying migration: {migration_name}")
            apply_migration(migration_file)
            record_migration(migration_name)

if __name__ == "__main__":
    run_migrations()
```

### Seeding Data

```python
# scripts/seed_data.py
from pricing.repository import PricingRepository

def seed_development_data():
    """Seed database with development data."""
    repo = PricingRepository()
    
    # Create providers
    providers = [
        {"name": "Graphisoft", "description": "BIM software solutions"},
        {"name": "Allplan", "description": "Construction software"},
        {"name": "Vectorworks", "description": "Design software"}
    ]
    
    for provider in providers:
        repo.create_provider(provider)
    
    # Create sample products and pricing
    sample_data = load_sample_data()
    repo.bulk_insert_pricing(sample_data)

if __name__ == "__main__":
    seed_development_data()
```

## Debugging

### Agent Debugging with LangGraph Studio

```python
# agent/debug_config.py
from langgraph.debug import DebugConfig

debug_config = DebugConfig(
    enabled=True,
    step_timeout=30,
    max_steps=50,
    save_artifacts=True
)

# Start with debugging enabled
langgraph dev --debug --config debug_config.py
```

### Service Debugging

#### Enable Debug Logging

```python
# Common logging setup
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Service-specific loggers
logger = logging.getLogger(__name__)
```

#### Debug Endpoints

Add debug endpoints to services:

```python
# Debug endpoints for development
@app.route("/debug/health")
def debug_health():
    """Extended health check with service details."""
    return {
        "status": "healthy",
        "version": os.getenv("VERSION", "dev"),
        "database": check_database_connection(),
        "memory_usage": get_memory_usage(),
        "uptime": get_uptime()
    }

@app.route("/debug/config")
def debug_config():
    """Show current configuration (sanitized)."""
    config = get_current_config()
    # Remove sensitive information
    sanitized = {k: v for k, v in config.items() if "key" not in k.lower()}
    return sanitized
```

## Performance Optimization

### Database Optimization

#### Query Performance

```python
# Use query analysis tools
def analyze_slow_queries():
    """Analyze slow queries for optimization."""
    query = """
    SELECT query, calls, total_time, mean_time
    FROM pg_stat_statements
    WHERE mean_time > 100
    ORDER BY mean_time DESC;
    """
    return execute_query(query)

# Add database indexes
def create_performance_indexes():
    """Create indexes for better query performance."""
    indexes = [
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pricing_product_country ON pricing(product_id, country_id);",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_provider_category ON products(provider_id, category);",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pricing_valid_dates ON pricing(valid_from, valid_to);"
    ]
    
    for index in indexes:
        execute_query(index)
```

#### Connection Pooling

```python
# database/pool.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

### Caching Strategies

```python
# caching/redis_cache.py
import redis
import json
from typing import Any, Optional

class RedisCache:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        value = self.redis.get(key)
        return json.loads(value) if value else None
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> None:
        """Set value in cache with TTL."""
        self.redis.setex(key, ttl, json.dumps(value))
    
    def invalidate_pattern(self, pattern: str) -> None:
        """Invalidate keys matching pattern."""
        keys = self.redis.keys(pattern)
        if keys:
            self.redis.delete(*keys)

# Usage in services
cache = RedisCache(REDIS_URL)

def get_product_pricing(product_id: int, country: str):
    cache_key = f"pricing:{product_id}:{country}"
    cached_result = cache.get(cache_key)
    
    if cached_result:
        return cached_result
    
    result = calculate_pricing(product_id, country)
    cache.set(cache_key, result, ttl=1800)  # 30 minutes
    return result
```

## Monitoring and Observability

### Application Metrics

```python
# monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_CONNECTIONS = Gauge('active_database_connections', 'Active database connections')

# Middleware for tracking
def track_request_metrics(app):
    @app.middleware("http")
    async def metrics_middleware(request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        duration = time.time() - start_time
        REQUEST_DURATION.observe(duration)
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path,
            status=response.status_code
        ).inc()
        
        return response
```

### Health Checks

```python
# health/checks.py
from typing import Dict, Any

async def check_database_health() -> Dict[str, Any]:
    """Check database connectivity and performance."""
    try:
        start_time = time.time()
        await execute_query("SELECT 1")
        response_time = time.time() - start_time
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time * 1000, 2)
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }

async def check_external_services() -> Dict[str, Any]:
    """Check external service dependencies."""
    services = {}
    
    # Check OpenAI API
    try:
        # Simple API call
        services["openai"] = {"status": "healthy"}
    except Exception as e:
        services["openai"] = {"status": "unhealthy", "error": str(e)}
    
    return services
```

## Contributing

### Git Workflow

We use a feature branch workflow:

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new pricing calculation method"

# Push branch
git push origin feature/your-feature-name

# Create pull request on GitHub
```

### Commit Message Convention

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(pricing): add support for bulk pricing calculations
fix(agent): resolve memory leak in conversation state
docs: update deployment guide for Kubernetes
test(search): add integration tests for vector search
```

### Pull Request Process

1. **Create Feature Branch**: Based on `main` branch
2. **Implement Changes**: Follow coding standards and add tests
3. **Update Documentation**: Update relevant docs
4. **Run Tests**: Ensure all tests pass
5. **Create PR**: With clear description and link to issues
6. **Code Review**: Address feedback from reviewers
7. **Merge**: Squash and merge after approval

### Code Review Guidelines

#### For Authors
- Keep PRs focused and reasonably sized
- Write clear commit messages and PR descriptions
- Add tests for new functionality
- Update documentation as needed
- Respond promptly to feedback

#### For Reviewers
- Check for code quality and adherence to standards
- Verify tests cover new functionality
- Consider security implications
- Provide constructive feedback
- Test locally if significant changes

## Troubleshooting Common Issues

### Development Setup Issues

**Python virtual environment issues**
```bash
# Remove and recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

**Node.js dependency issues**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
pnpm install
```

**Docker issues**
```bash
# Reset Docker environment
docker-compose down -v
docker system prune -f
docker-compose up --build
```

### Database Issues

**Connection refused**
```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check port availability
lsof -i :5432

# Restart database service
docker-compose restart postgres
```

**Migration failures**
```bash
# Check migration status
python scripts/check_migrations.py

# Rollback and retry
python scripts/rollback_migration.py
python scripts/migrate.py
```

### Service Communication Issues

**Service discovery failures**
```bash
# Check network connectivity
docker-compose exec agent ping pricing

# Check service logs
docker-compose logs pricing
docker-compose logs agent

# Verify environment variables
docker-compose exec agent env | grep SERVICE_URL
```
