---
title: FAQ & Troubleshooting
layout: default
nav_order: 7
---

# FAQ & Troubleshooting

Frequently asked questions and solutions to common issues when working with the Sales Agent system.

## Frequently Asked Questions

### General Questions

#### Q: What is the Sales Agent system?
A: The Sales Agent is an AI-powered system designed to assist with sales operations through natural language interactions. It provides intelligent conversations, product information, pricing calculations, and semantic search capabilities across multiple software vendors.

#### Q: Which software vendors are supported?
A: Currently supported vendors include:
- **Graphisoft** (Archicad, BIMcloud, BIMx)
- **Allplan** (Architecture, Engineering, Precast)
- **Vectorworks** (Architect, Landmark, Spotlight, Designer)
- **Bluebeam** (Revu, Core, Gateway)
- **Solibri** (Model Checker, Model Viewer, Site)

#### Q: Can I add support for additional vendors?
A: Yes! The system is designed to be extensible. See the [Development guide](development.html) for instructions on adding new vendors to the pricing service and updating the documentation corpus.

#### Q: What technologies does the system use?
A: The system uses:
- **LangGraph** for AI agent orchestration
- **Next.js** for the frontend interface
- **PostgreSQL & TimescaleDB** for data storage
- **Docker & Kubernetes** for deployment
- **Various AI APIs** (OpenAI, Azure OpenAI, Google AI)

### Deployment Questions

#### Q: What are the minimum system requirements?
A: For development:
- 8GB RAM
- 20GB disk space
- Docker & Docker Compose
- Python 3.9+ and Node.js 18+

For production:
- 16GB RAM (minimum)
- 100GB disk space
- Kubernetes cluster or Docker Swarm
- Load balancer and persistent storage

#### Q: Can I run this in the cloud?
A: Yes! The system supports deployment on:
- **AWS** (EKS, RDS, ElastiCache)
- **Azure** (AKS, Azure Database, Redis Cache)
- **Google Cloud** (GKE, Cloud SQL, Memorystore)
- **Any Kubernetes cluster**

#### Q: How do I get API keys for the AI services?
A: You'll need API keys for:
- **OpenAI**: Sign up at [platform.openai.com](https://platform.openai.com)
- **Azure OpenAI**: Apply through Azure portal
- **Google AI**: Get key from [AI Studio](https://aistudio.google.com)
- **LangSmith**: Register at [smith.langchain.com](https://smith.langchain.com)

### Technical Questions

#### Q: How does the semantic search work?
A: The search service uses:
1. **Vector embeddings** generated from documents
2. **Similarity search** using cosine distance
3. **Query enhancement** with multiple variants
4. **Result deduplication** and ranking
5. **AI-powered report generation** with references

#### Q: Can I customize the AI agent's behavior?
A: Yes! You can:
- Modify the LangGraph workflow in `agent/src/agent/graph.py`
- Add custom tools and integrations
- Adjust conversation prompts and personalities
- Configure response formats and styles

#### Q: How is data synchronized between services?
A: Services communicate through:
- **HTTP APIs** for real-time requests
- **Shared databases** for persistent data
- **Message queues** for asynchronous processing
- **Model Context Protocol (MCP)** for AI tool integration

## Common Issues and Solutions

### Installation & Setup Issues

#### Docker/Docker Compose Problems

**Issue**: Services fail to start with port conflicts
```bash
Error: bind: address already in use
```

**Solution**:
```bash
# Check what's using the port
lsof -i :3000
lsof -i :8123

# Kill the process or change ports in docker-compose.yml
docker-compose down
# Edit ports in docker-compose.yml if needed
docker-compose up -d
```

**Issue**: Docker build fails with permission errors
```bash
Error: permission denied while trying to connect to Docker daemon
```

**Solution**:
```bash
# Add user to docker group (Linux/macOS)
sudo usermod -aG docker $USER
newgrp docker

# On macOS, ensure Docker Desktop is running
open /Applications/Docker.app
```

#### Python Environment Issues

**Issue**: Package installation fails
```bash
ERROR: Could not install packages due to an EnvironmentError
```

**Solution**:
```bash
# Create fresh virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Upgrade pip first
pip install --upgrade pip

# Install packages
pip install -r requirements.txt
```

**Issue**: ModuleNotFoundError for local packages
```bash
ModuleNotFoundError: No module named 'agent'
```

**Solution**:
```bash
# Install in development mode
cd agent
pip install -e .

# Or add to PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

#### Node.js/Frontend Issues

**Issue**: pnpm command not found
```bash
bash: pnpm: command not found
```

**Solution**:
```bash
# Install pnpm globally
npm install -g pnpm

# Or use npm instead
npm install
npm run dev
```

**Issue**: TypeScript compilation errors
```bash
Type error: Cannot find module '@/components/ui'
```

**Solution**:
```bash
# Check tsconfig.json paths are correct
# Reinstall dependencies
rm -rf node_modules package-lock.json
pnpm install

# Clear Next.js cache
rm -rf .next
pnpm dev
```

### Service Communication Issues

#### Agent Service Problems

**Issue**: Agent fails to connect to pricing/search services
```bash
ConnectionError: HTTPConnectionPool: Max retries exceeded
```

**Solution**:
```bash
# Check if services are running
docker-compose ps

# Check service URLs in environment
echo $PRICING_SERVICE_URL
echo $SEARCH_SERVICE_URL

# Test connectivity
curl http://localhost:8001/health
curl http://localhost:8002/health

# Check Docker network
docker network ls
docker-compose exec agent ping pricing
```

**Issue**: LangGraph Studio not accessible
```bash
This site can't be reached - localhost:8123
```

**Solution**:
```bash
# Check if agent is running
docker-compose logs agent

# Verify port binding
docker-compose ps agent

# Try different port
langgraph dev --port 8124

# Check firewall settings
```

#### Database Connection Issues

**Issue**: PostgreSQL connection refused
```bash
psycopg2.OperationalError: could not connect to server
```

**Solution**:
```bash
# Check PostgreSQL is running
docker-compose ps postgres

# Check connection parameters
echo $POSTGRES_HOST
echo $POSTGRES_PORT

# Test connection manually
docker-compose exec postgres psql -U postgres -d sales_agent -c "SELECT 1;"

# Restart PostgreSQL
docker-compose restart postgres
```

**Issue**: TimescaleDB vector extension not found
```bash
ERROR: extension "vector" is not available
```

**Solution**:
```bash
# Use correct TimescaleDB image with pgvector
# In docker-compose.yml:
services:
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    # OR
    image: ankane/pgvector:v0.5.1

# Install extension manually
docker-compose exec timescaledb psql -U postgres -c "CREATE EXTENSION IF NOT EXISTS vector;"
```

### API and Authentication Issues

#### API Key Problems

**Issue**: OpenAI API key invalid
```bash
AuthenticationError: Incorrect API key provided
```

**Solution**:
```bash
# Verify API key format (starts with sk-)
echo $OPENAI_API_KEY

# Check for whitespace/special characters
export OPENAI_API_KEY=$(echo $OPENAI_API_KEY | tr -d '[:space:]')

# Test key directly
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"
```

**Issue**: Azure OpenAI endpoint errors
```bash
The API deployment for this resource does not exist
```

**Solution**:
```bash
# Check deployment name matches model
echo $AZURE_OPENAI_DEPLOYMENT_NAME

# Verify endpoint URL format
echo $AZURE_OPENAI_ENDPOINT
# Should be: https://your-resource.openai.azure.com/

# Check API version
echo $AZURE_OPENAI_API_VERSION
# Should be: 2024-02-15-preview or later
```

#### Rate Limiting Issues

**Issue**: API rate limits exceeded
```bash
RateLimitError: Rate limit reached for default-gpt-4
```

**Solution**:
```bash
# Implement retry with exponential backoff
# Add delay between requests
# Use different API keys for load distribution
# Consider upgrading API plan

# For development, reduce concurrent requests
export MAX_CONCURRENT_REQUESTS=2
```

### Performance Issues

#### Slow Response Times

**Issue**: Agent responses are very slow
```bash
# Responses taking >30 seconds
```

**Solution**:
```bash
# Check database performance
docker-compose exec postgres psql -U postgres -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;"

# Optimize database indexes
# Add connection pooling
# Scale services horizontally
# Use caching (Redis)
```

#### Memory Issues

**Issue**: Services running out of memory
```bash
MemoryError: Unable to allocate memory
```

**Solution**:
```bash
# Monitor memory usage
docker stats

# Increase Docker memory limits
# In docker-compose.yml:
services:
  agent:
    deploy:
      resources:
        limits:
          memory: 2G

# Optimize Python memory usage
export PYTHONMALLOC=malloc
```

#### Vector Search Performance

**Issue**: Search queries are slow
```bash
# Vector searches taking >5 seconds
```

**Solution**:
```sql
-- Check if vector index exists
\d+ documents

-- Create vector index if missing
CREATE INDEX CONCURRENTLY idx_documents_embedding 
ON documents USING ivfflat (embedding vector_cosine_ops);

-- Optimize index parameters
ALTER INDEX idx_documents_embedding SET (lists = 100);

-- Update table statistics
ANALYZE documents;
```

### Development Issues

#### Hot Reload Not Working

**Issue**: Changes not reflected in development
```bash
# Code changes not updating
```

**Solution**:
```bash
# For Python services
# Ensure you're using development mode
langgraph dev --watch

# For Next.js
# Clear cache and restart
rm -rf .next
pnpm dev

# Check file permissions
ls -la src/
```

#### Test Failures

**Issue**: Tests failing after changes
```bash
FAILED tests/test_pricing.py::test_calculate_price
```

**Solution**:
```bash
# Run tests with verbose output
pytest -v --tb=short

# Run specific test
pytest tests/test_pricing.py::test_calculate_price -v

# Update test database
pytest --create-db

# Check test environment variables
cat .env.test
```

## Getting Help

### Debugging Steps

1. **Check Service Health**
   ```bash
   curl http://localhost:8123/health  # Agent
   curl http://localhost:8001/health  # Pricing
   curl http://localhost:8002/health  # Search
   curl http://localhost:3000/api/health  # UI
   ```

2. **Review Logs**
   ```bash
   docker-compose logs agent
   docker-compose logs pricing
   docker-compose logs search
   docker-compose logs chat-ui
   ```

3. **Test Components Individually**
   ```bash
   # Test database
   docker-compose exec postgres psql -U postgres -c "SELECT 1;"
   
   # Test API endpoints
   curl http://localhost:8001/products
   
   # Test search
   python search/src/justsearch.py "test query"
   ```

4. **Verify Configuration**
   ```bash
   # Check environment variables
   docker-compose exec agent env | grep API_KEY
   
   # Verify file permissions
   ls -la agent/src/
   
   # Check Docker network
   docker network inspect sales-agent_default
   ```

### Support Resources

- **GitHub Issues**: [Report bugs or request features](https://github.com/Nemetschek-SE/sales-agent/issues)
- **Development Guide**: Detailed setup and development instructions
- **Service Documentation**: Individual service configuration guides
- **Architecture Overview**: Understanding system design and data flow

### Performance Monitoring

#### Set Up Monitoring

```bash
# Enable metrics collection
export ENABLE_METRICS=true

# Access metrics endpoints
curl http://localhost:8123/metrics  # Agent metrics
curl http://localhost:8001/metrics  # Pricing metrics
curl http://localhost:8002/metrics  # Search metrics
```

#### Monitor Key Metrics

- **Response Time**: API endpoint response times
- **Error Rate**: Failed requests percentage
- **Database Performance**: Query execution time
- **Memory Usage**: Service memory consumption
- **API Usage**: Token consumption and rate limits

### Best Practices for Troubleshooting

1. **Start Simple**: Test individual components before testing the full system
2. **Check Logs First**: Service logs often contain the root cause
3. **Verify Configuration**: Environment variables are a common source of issues
4. **Test Connectivity**: Ensure services can communicate with each other
5. **Monitor Resources**: Check CPU, memory, and disk usage
6. **Use Health Checks**: Implement and regularly check service health endpoints

### When to Seek Help

Create a GitHub issue when:
- You've followed troubleshooting steps without success
- You encounter a bug or unexpected behavior
- You need help with a specific use case or configuration
- You want to suggest improvements or new features

Include in your issue:
- **Environment details** (OS, Docker version, etc.)
- **Error messages** (full stack traces)
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Configuration files** (sanitized)
