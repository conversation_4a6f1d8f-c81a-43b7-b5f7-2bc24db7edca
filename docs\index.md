---
title: Home
layout: home
---

# Sales Agent Documentation

Welcome to the Sales Agent project documentation. This project is a comprehensive AI-powered sales agent system built with modern technologies.

## Project Components

The Sales Agent system consists of several key components:

- **Agent**: The core AI agent built with Lang<PERSON><PERSON>h
- **Chat UI**: A Next.js-based user interface for interacting with the agent
- **Pricing Service**: MCP server for handling pricing calculations
- **Search Service**: Vector search capabilities for enhanced information retrieval
- **Scraping**: Data collection and processing tools

## Getting Started

This documentation site is built with <PERSON><PERSON><PERSON> using the [Just the Docs] theme and is automatically deployed via GitHub Actions.

To explore the project:

1. Browse the documentation sections in the navigation
2. Check out the [GitHub repository] for the complete source code
3. Follow the setup guides for each component

[Just the Docs]: https://just-the-docs.github.io/just-the-docs/
[GitHub repository]: https://github.com/Nemetschek-SE/sales-agent
[GitHub Pages]: https://docs.github.com/en/pages
[README]: https://github.com/just-the-docs/just-the-docs-template/blob/main/README.md
[Jekyll]: https://jekyllrb.com
[GitHub Pages / Actions workflow]: https://github.blog/changelog/2022-07-27-github-pages-custom-github-actions-workflows-beta/
[use this template]: https://github.com/just-the-docs/just-the-docs-template/generate
