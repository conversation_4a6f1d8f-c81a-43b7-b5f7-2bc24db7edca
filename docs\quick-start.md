---
title: Quick Start
layout: default
nav_order: 3
---

# Quick Start Guide

Get the Sales Agent system up and running in minutes with this step-by-step guide.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker & Docker Compose** (for containerized deployment)
- **Python 3.9+** (for local development)
- **Node.js 18+** (for the UI component)
- **pnpm** (package manager for the UI)
- **Git** (for cloning the repository)

## Option 1: Docker Compose (Recommended)

The fastest way to get everything running is with Docker Compose:

### 1. Clone the Repository

```bash
git clone https://github.com/Nemetschek-SE/sales-agent.git
cd sales-agent
```

### 2. Set Up Environment Variables

Create environment files for each service:

```bash
# Agent service
cp agent/.env.example agent/.env

# Pricing service
cp pricing/.env.example pricing/.env

# Search service  
cp search/.env.example search/.env
```

### 3. Configure API Keys

Edit the `.env` files with your API keys:

```bash
# In agent/.env
OPENAI_API_KEY=your_openai_key_here
LANGSMITH_API_KEY=your_langsmith_key_here

# In search/.env
AZURE_OPENAI_API_KEY=your_azure_openai_key
GOOGLE_AI_API_KEY=your_google_ai_key

# In pricing/.env
DATABASE_URL=****************************************/pricing_db
```

### 4. Start All Services

```bash
# Start the complete system
docker-compose up -d

# Check service status
docker-compose ps
```

### 5. Access the Application

- **Chat UI**: http://localhost:3000
- **LangGraph Studio**: http://localhost:8123
- **Pricing API**: http://localhost:8001
- **Search API**: http://localhost:8002

## Option 2: Local Development

For development and debugging, you can run services locally:

### 1. Set Up the Agent

```bash
cd agent
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -e .
pip install "langgraph-cli[inmem]"

# Start the agent server
langgraph dev
```

### 2. Set Up the Chat UI

```bash
cd agent-chat-ui
pnpm install
pnpm dev
```

### 3. Set Up Pricing Service

```bash
cd pricing
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Start PostgreSQL with Docker
docker-compose up -d

# Run database migrations
python src/create_tables.py

# Start the pricing server
python src/mcp_server.py
```

### 4. Set Up Search Service

```bash
cd search
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Start the search server
python src/mcp_server.py
```

## Initial Configuration

### 1. Connect to the Agent

1. Open http://localhost:3000 in your browser
2. Enter the following configuration:
   - **Deployment URL**: `http://localhost:8123`
   - **Assistant/Graph ID**: `agent`
   - **LangSmith API Key**: (your API key if using hosted services)

### 2. Test the System

Try these sample queries:

```
"What is the pricing for Archicad in Germany?"
"Tell me about Vectorworks features"
"Compare Allplan and Graphisoft products"
```

## Troubleshooting

### Common Issues

**Services not starting?**
- Check Docker is running: `docker --version`
- Verify ports are available: `netstat -an | grep :3000`
- Check logs: `docker-compose logs [service-name]`

**Database connection errors?**
- Ensure PostgreSQL is running: `docker-compose ps postgres`
- Check connection string in `.env` files
- Verify database migrations ran successfully

**API key errors?**
- Verify all required API keys are set in `.env` files
- Check API key permissions and quotas
- Ensure no trailing spaces in environment variables

**UI not connecting to agent?**
- Verify agent server is running on port 8123
- Check browser console for connection errors
- Ensure CORS is configured properly

### Getting Help

If you encounter issues:

1. **Check the logs**: `docker-compose logs [service-name]`
2. **Verify configuration**: Ensure all `.env` files are properly configured
3. **Restart services**: `docker-compose restart [service-name]`
4. **Clean rebuild**: `docker-compose down && docker-compose up --build`

## Next Steps

Once you have the system running:

1. **Explore the Architecture**: Learn about the [system components](architecture.html)
2. **Set Up Development**: Follow the [development guide](development.html)
3. **Configure Services**: Customize each [service configuration](services/index.html)
4. **Deploy to Production**: Follow the [deployment guide](deployment.html)

## Useful Commands

```bash
# View all running services
docker-compose ps

# Follow logs for all services
docker-compose logs -f

# Restart a specific service
docker-compose restart agent

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up --build

# Access a service shell
docker-compose exec agent bash
```
