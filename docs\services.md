---
title: Services
layout: default
nav_order: 4
has_children: true
---

# Services Overview

The Sales Agent system is composed of several microservices, each with a specific responsibility. This section provides detailed documentation for each service.

## Service Architecture

Each service is designed to be:
- **Independent**: Can be developed, tested, and deployed separately
- **Scalable**: Supports horizontal scaling based on demand
- **Configurable**: Environment-based configuration for different deployments
- **Observable**: Comprehensive logging and monitoring capabilities

## Available Services

### [🤖 Agent Service](agent.html)
The core LangGraph-based AI agent that orchestrates conversations and coordinates with other services.

### [🎨 Chat UI](chat-ui.html) 
A Next.js frontend application providing the user interface for interacting with the sales agent.

### [💰 Pricing Service](pricing.html)
A Model Context Protocol (MCP) server that handles product pricing calculations and queries.

### [🔍 Search Service](search.html)
A semantic search service with AI-powered report generation capabilities.

### [🕷️ Scraping Service](scraping.html)
Web scraping infrastructure for collecting product documentation and information.

### [📚 Document Processing](document-processing.html)
Service for processing scraped content and ingesting it into the vector store.

## Service Communication

Services communicate through:
- **HTTP APIs**: RESTful endpoints for service-to-service communication
- **MCP Protocol**: Model Context Protocol for AI agent tool integration
- **Shared Databases**: TimescaleDB for vector data, PostgreSQL for structured data
- **Message Queues**: For asynchronous processing (when applicable)

## Common Configuration

All services support:
- **Environment Variables**: For configuration management
- **Docker Containers**: For consistent deployment
- **Health Checks**: For monitoring service availability
- **Graceful Shutdown**: For safe service restarts

## Development Workflow

When working with services:

1. **Local Development**: Run services individually for debugging
2. **Integration Testing**: Use Docker Compose for full-stack testing  
3. **Service Mocking**: Mock external dependencies during development
4. **API Documentation**: Each service provides OpenAPI/Swagger documentation
