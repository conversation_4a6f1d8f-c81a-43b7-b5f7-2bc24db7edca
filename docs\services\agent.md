---
title: Agent Service
layout: default
parent: Services
nav_order: 1
---

# Agent Service

The Agent Service is the core component of the Sales Agent system, built using [LangGraph](https://github.com/langchain-ai/langgraph) to provide intelligent conversation orchestration and decision-making capabilities.

## Overview

The agent acts as the central coordinator, processing user queries, determining which tools to use, and synthesizing responses from various services. It maintains conversation state and provides a natural language interface for complex sales operations.

## Architecture

### Core Components

- **Graph Definition**: Located in `src/agent/graph.py`
- **Configuration**: Managed through `src/tools_agent/configuration.py`
- **Utilities**: Helper functions in `src/agent/util/`

### Key Features

- **Multi-step Reasoning**: Plans and executes complex queries
- **Tool Integration**: Seamlessly calls pricing and search services
- **State Management**: Maintains conversation context across interactions
- **Visual Debugging**: Integrates with LangGraph Studio for development

## Installation

### Prerequisites

- Python 3.9+
- LangGraph CLI
- Access to OpenAI API (or compatible LLM service)

### Setup

```bash
cd agent

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e .
pip install "langgraph-cli[inmem]"
```

### Environment Configuration

Create a `.env` file:

```bash
# Core Configuration
OPENAI_API_KEY=your_openai_api_key_here
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Service URLs (for production)
PRICING_SERVICE_URL=http://pricing:8001
SEARCH_SERVICE_URL=http://search:8002

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
```

## Running the Service

### Development Mode

```bash
# Start with hot reload and debugging
langgraph dev

# With specific configuration
langgraph dev --config langgraph.json
```

The service will be available at:
- **API Endpoint**: http://localhost:8123
- **LangGraph Studio**: http://localhost:8123/studio

### Production Mode

```bash
# Build the Docker image
langgraph build -t sales-agent:latest

# Run the container
docker run -p 8123:8123 --env-file .env sales-agent:latest
```

## Configuration

### Graph Configuration

The `langgraph.json` file defines the agent configuration:

```json
{
  "dependencies": ["."],
  "graphs": {
    "agent": "./src/agent/graph.py:graph"
  },
  "env": ".env"
}
```

### Agent Tools

The agent has access to several tools:

#### Pricing Tool
- **Purpose**: Retrieve product pricing information
- **Endpoint**: Calls the Pricing Service MCP server
- **Parameters**: Product name, country, currency

#### Search Tool  
- **Purpose**: Perform semantic search and generate reports
- **Endpoint**: Calls the Search Service
- **Parameters**: Query text, search scope

#### Configuration Tool
- **Purpose**: Access system configuration
- **Source**: Local configuration management

## API Reference

### Main Endpoints

#### POST /invoke
Invoke the agent with a single message:

```bash
curl -X POST http://localhost:8123/invoke \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "messages": [
        {"role": "user", "content": "What is the price of Archicad in Germany?"}
      ]
    }
  }'
```

#### POST /stream
Stream responses for real-time interaction:

```bash
curl -X POST http://localhost:8123/stream \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "messages": [
        {"role": "user", "content": "Tell me about Vectorworks features"}
      ]
    }
  }'
```

### Thread Management

#### POST /threads
Create a new conversation thread:

```bash
curl -X POST http://localhost:8123/threads \
  -H "Content-Type: application/json" \
  -d '{
    "metadata": {"user_id": "user123"}
  }'
```

#### GET /threads/{thread_id}/runs
Get conversation history for a thread:

```bash
curl http://localhost:8123/threads/thread_123/runs
```

## Development

### Project Structure

```
agent/
├── src/
│   ├── agent/
│   │   ├── __init__.py
│   │   ├── graph.py          # Main graph definition
│   │   └── util/             # Utility functions
│   └── tools_agent/
│       ├── configuration.py  # Configuration management
│       └── graph.py          # Alternative graph implementation
├── static/
│   └── studio_ui.png         # Documentation assets
├── tests/
│   ├── conftest.py           # Test configuration
│   ├── integration_tests/    # Integration tests
│   └── unit_tests/           # Unit tests
├── docker-compose.yaml       # Local development setup
├── langgraph.json           # LangGraph configuration
├── Makefile                 # Development commands
└── pyproject.toml           # Python project configuration
```

### Testing

```bash
# Run unit tests
pytest tests/unit_tests/

# Run integration tests
pytest tests/integration_tests/

# Run all tests with coverage
pytest --cov=src tests/
```

### Debugging

#### Using LangGraph Studio

1. Start the development server: `langgraph dev`
2. Open http://localhost:8123/studio
3. Create a new thread and send test messages
4. Use the visual debugger to step through the graph execution

#### Logging

The agent provides detailed logging:

```python
import logging

# Configure logging level
logging.basicConfig(level=logging.DEBUG)

# Agent-specific logs
logger = logging.getLogger("agent")
logger.info("Processing user query: %s", user_input)
```

## Customization

### Adding New Tools

1. Create a new tool function:

```python
from langchain_core.tools import tool

@tool
def my_custom_tool(query: str) -> str:
    """Custom tool description."""
    # Tool implementation
    return result
```

2. Register the tool in the graph:

```python
# In src/agent/graph.py
from .tools import my_custom_tool

# Add to agent tools
agent = create_agent(llm, [pricing_tool, search_tool, my_custom_tool])
```

### Modifying Agent Behavior

Edit the graph definition in `src/agent/graph.py`:

```python
def create_graph():
    # Define workflow nodes
    workflow = StateGraph(AgentState)
    
    # Add custom nodes
    workflow.add_node("custom_node", custom_function)
    
    # Define edges and conditions
    workflow.add_edge("start", "custom_node")
    
    return workflow.compile()
```

## Troubleshooting

### Common Issues

**LangGraph CLI not found**
```bash
pip install "langgraph-cli[inmem]"
```

**Import errors**
```bash
# Reinstall in development mode
pip install -e .
```

**Tool execution failures**
- Check service URLs in environment variables
- Verify API keys are correctly configured
- Check service logs for connection issues

**Memory issues with large conversations**
- Implement conversation summarization
- Set maximum context window limits
- Use state pruning strategies

### Performance Optimization

- **Caching**: Cache tool results for repeated queries
- **Parallel Execution**: Use async tool calls where possible  
- **Streaming**: Implement streaming responses for better UX
- **Model Selection**: Choose appropriate model sizes for different tasks

## Production Considerations

### Security
- Use secure API key storage (e.g., secrets management)
- Implement rate limiting for API endpoints
- Validate and sanitize all user inputs

### Monitoring
- Set up health checks: `GET /health`
- Monitor response times and error rates
- Track token usage and costs

### Scaling
- Use horizontal pod autoscaling in Kubernetes
- Implement connection pooling for databases
- Consider using a message queue for high-volume deployments
