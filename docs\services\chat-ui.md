---
title: Chat UI
layout: default
parent: Services
nav_order: 2
---

# Chat UI Service

The Chat UI is a modern Next.js application that provides an intuitive interface for interacting with the Sales Agent. Built with React, TypeScript, and Tailwind CSS, it offers a responsive and feature-rich chat experience.

## Overview

The Chat UI serves as the primary user interface for the Sales Agent system, enabling users to:
- Engage in natural language conversations with the AI agent
- Upload files and documents for analysis
- Manage conversation threads
- View structured responses with proper formatting

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom component library
- **State Management**: React hooks and context
- **HTTP Client**: Fetch API with custom hooks

## Installation

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- Access to a running LangGraph server

### Setup

```bash
cd agent-chat-ui

# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env.local
```

### Environment Configuration

Create a `.env.local` file:

```bash
# Application Configuration
NEXT_PUBLIC_APP_NAME="Sales Agent Chat"
NEXT_PUBLIC_DEFAULT_MODEL="gpt-4"

# API Configuration (optional defaults)
NEXT_PUBLIC_DEFAULT_LANGGRAPH_URL="http://localhost:8123"
NEXT_PUBLIC_DEFAULT_ASSISTANT_ID="agent"

# Development Settings
NODE_ENV=development
```

## Running the Service

### Development Mode

```bash
# Start the development server
pnpm dev

# With specific port
pnpm dev -- --port 3001
```

The application will be available at http://localhost:3000

### Production Build

```bash
# Build for production
pnpm build

# Start production server
pnpm start
```

### Docker Deployment

```bash
# Build Docker image
docker build -t sales-agent-ui .

# Run container
docker run -p 3000:3000 sales-agent-ui
```

## Features

### Chat Interface

#### Real-time Messaging
- Streaming responses from the agent
- Message history persistence
- Typing indicators and loading states
- Error handling and retry mechanisms

#### Rich Content Support
- Markdown rendering for formatted responses
- Code syntax highlighting
- File attachments and previews
- Image and document display

#### Thread Management
- Create new conversation threads
- Switch between active conversations
- Delete and archive threads
- Export conversation history

### File Upload

The UI supports file uploads for document analysis:

```typescript
// Example file upload usage
const { uploadFile, isUploading } = useFileUpload();

const handleFileUpload = async (file: File) => {
  try {
    const result = await uploadFile(file);
    // Handle successful upload
  } catch (error) {
    // Handle upload error
  }
};
```

### Configuration Management

Users can configure connection settings:

- **Deployment URL**: LangGraph server endpoint
- **Assistant ID**: Specific agent/graph to use
- **API Key**: Authentication for hosted services

## Project Structure

```
agent-chat-ui/
├── src/
│   ├── app/                  # Next.js App Router
│   │   ├── api/             # API routes
│   │   ├── globals.css      # Global styles
│   │   ├── layout.tsx       # Root layout
│   │   └── page.tsx         # Home page
│   ├── components/          # React components
│   │   ├── icons/           # Icon components
│   │   ├── thread/          # Chat thread components
│   │   └── ui/              # UI primitives
│   ├── hooks/               # Custom React hooks
│   │   ├── use-file-upload.tsx
│   │   └── useLanggraphState.ts
│   ├── lib/                 # Utility libraries
│   └── providers/           # Context providers
├── public/                  # Static assets
│   └── logo.svg
├── components.json          # shadcn/ui configuration
├── next.config.mjs         # Next.js configuration
├── package.json            # Dependencies
├── tailwind.config.js      # Tailwind CSS configuration
└── tsconfig.json           # TypeScript configuration
```

## Key Components

### Chat Interface Components

#### MessageList
Renders the conversation history:

```typescript
interface MessageListProps {
  messages: Message[];
  isStreaming: boolean;
  onRetry?: (messageId: string) => void;
}

export function MessageList({ messages, isStreaming, onRetry }: MessageListProps) {
  // Component implementation
}
```

#### MessageInput
Handles user input and file uploads:

```typescript
interface MessageInputProps {
  onSendMessage: (content: string, files?: File[]) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function MessageInput({ onSendMessage, disabled, placeholder }: MessageInputProps) {
  // Component implementation
}
```

#### ThreadSidebar
Manages conversation threads:

```typescript
interface ThreadSidebarProps {
  threads: Thread[];
  activeThreadId?: string;
  onThreadSelect: (threadId: string) => void;
  onNewThread: () => void;
  onDeleteThread: (threadId: string) => void;
}

export function ThreadSidebar(props: ThreadSidebarProps) {
  // Component implementation
}
```

### Custom Hooks

#### useLanggraphState
Manages connection to the LangGraph server:

```typescript
export function useLanggraphState() {
  const [config, setConfig] = useState<LanggraphConfig>();
  const [isConnected, setIsConnected] = useState(false);
  
  const connect = async (url: string, assistantId: string, apiKey?: string) => {
    // Connection logic
  };
  
  const sendMessage = async (message: string, threadId?: string) => {
    // Message sending logic
  };
  
  return {
    config,
    isConnected,
    connect,
    sendMessage,
    // ... other methods
  };
}
```

#### useFileUpload
Handles file upload functionality:

```typescript
export function useFileUpload() {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  const uploadFile = async (file: File) => {
    // Upload implementation
  };
  
  return {
    uploadFile,
    isUploading,
    uploadProgress
  };
}
```

## Configuration

### Tailwind CSS

The project uses a custom Tailwind configuration:

```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Custom color palette
      },
      typography: {
        // Custom typography styles
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    // Other plugins
  ],
}
```

### Component Library

The UI uses shadcn/ui components configured in `components.json`:

```json
{
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/app/globals.css",
    "baseColor": "slate",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
```

## API Integration

### LangGraph Client

The UI communicates with the LangGraph server through a custom client:

```typescript
class LanggraphClient {
  constructor(
    private baseUrl: string,
    private apiKey?: string
  ) {}
  
  async createThread(): Promise<Thread> {
    // Create new conversation thread
  }
  
  async sendMessage(
    threadId: string,
    message: string,
    stream: boolean = true
  ): Promise<Response> {
    // Send message to agent
  }
  
  async getThreadHistory(threadId: string): Promise<Message[]> {
    // Retrieve conversation history
  }
}
```

### Streaming Responses

The UI supports streaming responses for real-time interaction:

```typescript
async function streamResponse(response: Response, onChunk: (chunk: string) => void) {
  const reader = response.body?.getReader();
  if (!reader) return;
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = new TextDecoder().decode(value);
      onChunk(chunk);
    }
  } finally {
    reader.releaseLock();
  }
}
```

## Customization

### Theming

Customize the appearance by modifying CSS variables:

```css
/* src/app/globals.css */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --secondary: 210 40% 96%;
  /* ... other variables */
}

[data-theme="dark"] {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  /* ... dark theme variables */
}
```

### Adding Custom Components

1. Create component in `src/components/`:

```typescript
// src/components/custom-widget.tsx
interface CustomWidgetProps {
  data: any;
}

export function CustomWidget({ data }: CustomWidgetProps) {
  return (
    <div className="p-4 border rounded-lg">
      {/* Widget content */}
    </div>
  );
}
```

2. Export from index file:

```typescript
// src/components/index.ts
export { CustomWidget } from './custom-widget';
```

### Environment-specific Configuration

Use Next.js environment variables for configuration:

```typescript
// src/lib/config.ts
export const config = {
  langgraphUrl: process.env.NEXT_PUBLIC_LANGGRAPH_URL || 'http://localhost:8123',
  assistantId: process.env.NEXT_PUBLIC_ASSISTANT_ID || 'agent',
  appName: process.env.NEXT_PUBLIC_APP_NAME || 'Sales Agent Chat',
};
```

## Development

### Code Style

The project uses ESLint and Prettier for code formatting:

```bash
# Lint code
pnpm lint

# Fix linting issues
pnpm lint:fix

# Format code
pnpm format
```

### Testing

```bash
# Run tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Generate coverage report
pnpm test:coverage
```

## Deployment

### Vercel (Recommended)

The easiest way to deploy is using Vercel:

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Deploy to production
vercel --prod
```

### Docker

For containerized deployment:

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies
FROM base AS deps
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Build application
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm install -g pnpm && pnpm build

# Production image
FROM base AS runner
WORKDIR /app
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
CMD ["node", "server.js"]
```

### Environment Variables for Production

```bash
# Production environment variables
NEXT_PUBLIC_LANGGRAPH_URL=https://your-agent-api.com
NEXT_PUBLIC_ASSISTANT_ID=production-agent
NEXT_PUBLIC_APP_NAME="Sales Agent"
```

## Troubleshooting

### Common Issues

**Connection errors to LangGraph server**
- Verify the server URL is correct and accessible
- Check if the agent service is running
- Ensure firewall/CORS settings allow connections

**File upload failures**
- Check file size limits
- Verify supported file types
- Ensure proper error handling is implemented

**Styling issues**
- Clear browser cache
- Verify Tailwind CSS classes are being generated
- Check for conflicting CSS rules

### Performance Optimization

- **Code splitting**: Implement dynamic imports for large components
- **Image optimization**: Use Next.js Image component
- **Caching**: Configure appropriate cache headers
- **Bundle analysis**: Use `@next/bundle-analyzer`

```bash
# Analyze bundle size
ANALYZE=true pnpm build
```
