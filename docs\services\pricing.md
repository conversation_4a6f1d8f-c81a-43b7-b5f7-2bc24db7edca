---
title: Pricing Service
layout: default
parent: Services
nav_order: 3
---

# Pricing Service

The Pricing Service is a Model Context Protocol (MCP) server that provides comprehensive product pricing information and calculations for various software vendors including Allplan, Graphisoft, Vectorworks, Bluebeam, and Solibri.

## Overview

This service acts as a centralized pricing database and calculation engine, supporting:
- Multi-vendor product pricing
- Country-specific pricing variations
- Currency conversion capabilities
- Historical pricing data
- Bulk pricing calculations

## Architecture

### Database Schema

The service uses PostgreSQL with the following main tables:

```sql
-- Countries table
CREATE TABLE countries (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    iso_code VARCHAR(2) UNIQUE NOT NULL,
    currency VARCHAR(3) NOT NULL
);

-- Providers table  
CREATE TABLE providers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);

-- Products table
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES providers(id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(100)
);

-- Pricing table
CREATE TABLE pricing (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id),
    country_id INTEGER REFERENCES countries(id),
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    valid_from DATE,
    valid_to DATE,
    license_type VARCHAR(50)
);
```

### Supported Vendors

#### Allplan
- **Products**: Allplan Architecture, Allplan Engineering, Allplan Precast
- **Regions**: Europe, North America, Asia-Pacific
- **License Types**: Perpetual, Subscription, Network

#### Graphisoft  
- **Products**: Archicad, BIMcloud, BIMx
- **Regions**: Global coverage
- **License Types**: Commercial, Educational, Trial

#### Vectorworks
- **Products**: Architect, Landmark, Spotlight, Designer
- **Regions**: Worldwide
- **License Types**: Perpetual, Subscription, Rental

#### Bluebeam
- **Products**: Revu, Core, Gateway
- **Regions**: North America, Europe
- **License Types**: Standard, CAD, eXtreme

#### Solibri
- **Products**: Model Checker, Model Viewer, Site
- **Regions**: Global
- **License Types**: Professional, Office, Team

## Installation

### Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Docker (for containerized deployment)

### Setup

```bash
cd pricing

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Database Setup

#### Using Docker Compose

```bash
# Start PostgreSQL
docker-compose up -d

# Create tables
python src/create_tables.py

# Load sample data
python src/example_pricing_data.py
```

#### Manual Setup

```bash
# Create database
createdb pricing_db

# Set environment variables
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB=pricing_db
export POSTGRES_USER=your_user
export POSTGRES_PASSWORD=your_password

# Initialize database
python src/create_tables.py
```

### Environment Configuration

Create a `.env` file:

```bash
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=pricing_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# Service Configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8001
LOG_LEVEL=INFO

# Currency API (optional for live rates)
CURRENCY_API_KEY=your_api_key
```

## Running the Service

### Development Mode

```bash
# Start the MCP server
python src/mcp_server.py

# With debug logging
LOG_LEVEL=DEBUG python src/mcp_server.py
```

### Production Mode

```bash
# Using Docker
docker build -t pricing-service .
docker run -p 8001:8001 --env-file .env pricing-service

# Using systemd (Linux)
sudo systemctl start pricing-service
```

## API Reference

### MCP Tools

The service exposes several tools through the Model Context Protocol:

#### get_product_pricing
Get pricing information for a specific product:

```json
{
  "name": "get_product_pricing",
  "description": "Get pricing for a specific product",
  "inputSchema": {
    "type": "object",
    "properties": {
      "product_name": {"type": "string"},
      "country": {"type": "string", "optional": true},
      "license_type": {"type": "string", "optional": true}
    }
  }
}
```

Example usage:
```python
result = await client.call_tool("get_product_pricing", {
    "product_name": "Archicad",
    "country": "Germany",
    "license_type": "Commercial"
})
```

#### search_products
Search for products by name or category:

```json
{
  "name": "search_products", 
  "description": "Search for products by name or category",
  "inputSchema": {
    "type": "object",
    "properties": {
      "query": {"type": "string"},
      "provider": {"type": "string", "optional": true},
      "category": {"type": "string", "optional": true}
    }
  }
}
```

#### compare_pricing
Compare pricing across multiple products or regions:

```json
{
  "name": "compare_pricing",
  "description": "Compare pricing across products or regions", 
  "inputSchema": {
    "type": "object",
    "properties": {
      "products": {"type": "array", "items": {"type": "string"}},
      "countries": {"type": "array", "items": {"type": "string"}},
      "license_type": {"type": "string", "optional": true}
    }
  }
}
```

#### get_currency_conversion
Convert prices between currencies:

```json
{
  "name": "get_currency_conversion",
  "description": "Convert price between currencies",
  "inputSchema": {
    "type": "object", 
    "properties": {
      "amount": {"type": "number"},
      "from_currency": {"type": "string"},
      "to_currency": {"type": "string"}
    }
  }
}
```

### REST API Endpoints

In addition to MCP tools, the service provides REST endpoints:

#### GET /products
List all available products:

```bash
curl http://localhost:8001/products

# With filters
curl "http://localhost:8001/products?provider=Graphisoft&category=BIM"
```

#### GET /products/{product_id}/pricing
Get pricing for a specific product:

```bash
curl http://localhost:8001/products/123/pricing

# With country filter
curl "http://localhost:8001/products/123/pricing?country=DE"
```

#### GET /providers
List all software providers:

```bash
curl http://localhost:8001/providers
```

#### GET /countries
List supported countries:

```bash
curl http://localhost:8001/countries
```

## Data Management

### Loading Pricing Data

The service includes data loaders for each vendor:

```python
# Load Allplan pricing data
from src.allplan_pricing_data import load_allplan_data
load_allplan_data()

# Load Graphisoft pricing data  
from src.graphisoft_pricing_data import load_graphisoft_data
load_graphisoft_data()

# Load all vendor data
python src/load_all_pricing_data.py
```

### Data Format

Pricing data is stored in JSON format:

```json
{
  "provider": "Graphisoft",
  "products": [
    {
      "name": "Archicad",
      "category": "BIM",
      "description": "Complete BIM solution for architects",
      "pricing": [
        {
          "country": "Germany", 
          "currency": "EUR",
          "license_type": "Commercial",
          "price": 4500.00,
          "valid_from": "2024-01-01",
          "valid_to": "2024-12-31"
        }
      ]
    }
  ]
}
```

### Updating Pricing Data

```python
from src.pricing_repository import PricingRepository

repo = PricingRepository()

# Update single product pricing
repo.update_product_pricing(
    product_id=123,
    country="Germany", 
    price=4700.00,
    valid_from="2024-06-01"
)

# Bulk update from file
repo.bulk_update_from_file("new_pricing_data.json")
```

## Configuration

### Database Configuration

```python
# src/database_config.py
DATABASE_CONFIG = {
    'host': os.getenv('POSTGRES_HOST', 'localhost'),
    'port': int(os.getenv('POSTGRES_PORT', 5432)),
    'database': os.getenv('POSTGRES_DB', 'pricing_db'),
    'user': os.getenv('POSTGRES_USER', 'postgres'),
    'password': os.getenv('POSTGRES_PASSWORD', ''),
    'pool_size': 10,
    'max_overflow': 20
}
```

### Service Configuration

```python
# src/config.py
class Config:
    MCP_SERVER_HOST = os.getenv('MCP_SERVER_HOST', '0.0.0.0')
    MCP_SERVER_PORT = int(os.getenv('MCP_SERVER_PORT', 8001))
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    # Currency conversion settings
    CURRENCY_API_KEY = os.getenv('CURRENCY_API_KEY')
    CURRENCY_CACHE_TTL = 3600  # 1 hour
    
    # Pricing calculation settings
    DEFAULT_CURRENCY = 'EUR'
    PRICE_PRECISION = 2
```

## Development

### Project Structure

```
pricing/
├── src/
│   ├── mcp_server.py           # Main MCP server
│   ├── pricing_repository.py   # Database operations
│   ├── pricing_calculator.py   # Price calculations
│   ├── providers.py            # Provider definitions
│   ├── countries.py            # Country/currency data
│   ├── create_tables.sql       # Database schema
│   ├── example_pricing_data.py # Sample data loader
│   └── vendor_data/
│       ├── allplan_pricing_data.json
│       ├── graphisoft_pricing_data.json
│       ├── vectorworks_pricing_data.json
│       ├── bluebeam_pricing_data.json
│       └── solibri_pricing_data.json
├── docker-compose.yml          # Development setup
├── Dockerfile                  # Container build
├── requirements.txt            # Python dependencies
└── README.md                  # Documentation
```

### Adding New Vendors

1. Create vendor data file:

```json
// src/vendor_data/newvendor_pricing_data.json
{
  "provider": "NewVendor",
  "products": [
    {
      "name": "Product Name",
      "category": "Category",
      "description": "Product description",
      "pricing": [...]
    }
  ]
}
```

2. Create data loader:

```python
# src/newvendor_pricing_data.py
def load_newvendor_data():
    with open('vendor_data/newvendor_pricing_data.json') as f:
        data = json.load(f)
    
    repo = PricingRepository()
    repo.load_vendor_data(data)
```

3. Register provider:

```python
# src/providers.py
PROVIDERS = {
    'newvendor': {
        'name': 'NewVendor',
        'description': 'NewVendor software solutions'
    }
}
```

### Testing

```bash
# Run unit tests
python -m pytest tests/unit/

# Run integration tests (requires database)
python -m pytest tests/integration/

# Test specific functionality
python -m pytest tests/test_pricing_calculator.py -v
```

### Monitoring

```python
# Health check endpoint
@app.route('/health')
def health_check():
    try:
        # Test database connection
        repo = PricingRepository()
        repo.test_connection()
        return {'status': 'healthy', 'database': 'connected'}
    except Exception as e:
        return {'status': 'unhealthy', 'error': str(e)}, 500
```

## Troubleshooting

### Common Issues

**Database connection errors**
```bash
# Check PostgreSQL is running
docker-compose ps postgres

# Test connection
psql -h localhost -U postgres -d pricing_db -c "SELECT 1;"
```

**Missing pricing data**
```bash
# Reload sample data
python src/example_pricing_data.py

# Check data was loaded
python -c "from src.pricing_repository import PricingRepository; print(PricingRepository().get_product_count())"
```

**Currency conversion failures**
- Verify CURRENCY_API_KEY is set
- Check internet connectivity for API access
- Use fallback conversion rates if API is unavailable

### Performance Optimization

- **Database Indexing**: Ensure proper indexes on frequently queried columns
- **Connection Pooling**: Use connection pooling for high-volume deployments
- **Caching**: Implement Redis for frequently accessed pricing data
- **Query Optimization**: Use EXPLAIN ANALYZE to optimize slow queries

```sql
-- Create performance indexes
CREATE INDEX idx_pricing_product_country ON pricing(product_id, country_id);
CREATE INDEX idx_pricing_valid_dates ON pricing(valid_from, valid_to);
CREATE INDEX idx_products_provider ON products(provider_id);
```
