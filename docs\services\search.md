---
title: Search Service
layout: default
parent: Services
nav_order: 4
---

# Search Service

The Search Service provides semantic search capabilities and AI-powered report generation for the Sales Agent system. It combines advanced vector search with multiple LLMs to deliver comprehensive, referenced documentation.

## Overview

The Search Service enables:
- **Semantic Search**: Vector-based similarity search across product documentation
- **Multi-LLM Integration**: Azure OpenAI for reasoning, Google AI for embeddings
- **Query Enhancement**: Automatic query rephrasing for improved recall
- **Report Generation**: Structured Markdown reports with references
- **Document Deduplication**: Intelligent removal of duplicate content

## Architecture

### Core Components

- **Vector Search Engine**: TimescaleDB with pgvector extension
- **Query Processor**: Multi-variant query generation
- **Report Generator**: AI-powered structured reporting
- **MCP Server**: Model Context Protocol integration

### Technology Stack

- **Database**: TimescaleDB with vector extensions
- **Embeddings**: Google AI `models/embedding-001`
- **LLM**: Azure OpenAI GPT-4
- **Framework**: Python with asyncio
- **Protocol**: Model Context Protocol (MCP)

## Installation

### Prerequisites

- Python 3.9+
- TimescaleDB with pgvector extension
- Azure OpenAI API access
- Google AI API access

### Setup

```bash
cd search

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Environment Configuration

Create a `.env` file:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_key

# TimescaleDB Configuration
TIMESCALE_HOST=localhost
TIMESCALE_PORT=5432
TIMESCALE_DB=vector_store
TIMESCALE_USER=postgres
TIMESCALE_PASSWORD=password

# Service Configuration
MCP_SERVER_HOST=0.0.0.0
MCP_SERVER_PORT=8002
LOG_LEVEL=INFO

# Search Configuration
MAX_SEARCH_RESULTS=50
SIMILARITY_THRESHOLD=0.7
QUERY_VARIANTS=3
```

## Running the Service

### Development Mode

```bash
# Start the MCP server
python src/mcp_server.py

# With debug logging
LOG_LEVEL=DEBUG python src/mcp_server.py

# Test the service
python src/justsearch.py "What are the features of Archicad?"
```

### Production Mode

```bash
# Using Docker
docker build -t search-service .
docker run -p 8002:8002 --env-file .env search-service

# Using systemd (Linux)
sudo systemctl start search-service
```

## Features

### Semantic Search

The service performs vector similarity search using embeddings:

```python
from src.vector_search import VectorSearch

# Initialize search engine
search = VectorSearch()

# Perform search
results = await search.search(
    query="Archicad BIM features",
    limit=10,
    similarity_threshold=0.7
)

for result in results:
    print(f"Score: {result.score}")
    print(f"Content: {result.content}")
    print(f"Source: {result.metadata.get('source_url')}")
```

### Query Enhancement

Automatic query rephrasing improves search recall:

```python
from src.rephrase import QueryRephraser

rephraser = QueryRephraser()

# Generate multiple query variants
variants = await rephraser.rephrase_query(
    "How much does Archicad cost?",
    num_variants=3
)

# Example output:
# [
#   "What is the pricing for Archicad software?",
#   "Archicad license cost and subscription fees",
#   "How to purchase Archicad and pricing options"
# ]
```

### Report Generation

AI-powered report generation with references:

```python
from src.report import ReportGenerator

generator = ReportGenerator()

# Generate comprehensive report
report = await generator.generate_report(
    query="Compare Archicad and Revit features",
    search_results=search_results
)

print(report.content)  # Markdown with references
print(report.references)  # List of source links
```

## API Reference

### MCP Tools

The service exposes several tools through the Model Context Protocol:

#### semantic_search
Perform semantic search across documents:

```json
{
  "name": "semantic_search",
  "description": "Search for relevant documents using semantic similarity",
  "inputSchema": {
    "type": "object",
    "properties": {
      "query": {"type": "string"},
      "limit": {"type": "integer", "default": 10},
      "threshold": {"type": "number", "default": 0.7},
      "categories": {"type": "array", "items": {"type": "string"}}
    },
    "required": ["query"]
  }
}
```

Example usage:
```python
result = await client.call_tool("semantic_search", {
    "query": "Vectorworks rendering capabilities",
    "limit": 15,
    "threshold": 0.8,
    "categories": ["features", "documentation"]
})
```

#### generate_report
Generate comprehensive reports with references:

```json
{
  "name": "generate_report",
  "description": "Generate a detailed report from search results",
  "inputSchema": {
    "type": "object",
    "properties": {
      "query": {"type": "string"},
      "include_references": {"type": "boolean", "default": true},
      "max_length": {"type": "integer", "default": 2000}
    },
    "required": ["query"]
  }
}
```

#### search_and_report
Combined search and report generation:

```json
{
  "name": "search_and_report", 
  "description": "Perform search and generate comprehensive report",
  "inputSchema": {
    "type": "object",
    "properties": {
      "query": {"type": "string"},
      "search_limit": {"type": "integer", "default": 20},
      "report_style": {"type": "string", "enum": ["detailed", "summary", "comparison"]}
    },
    "required": ["query"]
  }
}
```

### REST API Endpoints

#### POST /search
Direct search endpoint:

```bash
curl -X POST http://localhost:8002/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Archicad collaboration features",
    "limit": 10,
    "threshold": 0.7
  }'
```

Response:
```json
{
  "results": [
    {
      "content": "Archicad offers real-time collaboration...",
      "score": 0.89,
      "metadata": {
        "source_url": "https://example.com/archicad-features",
        "title": "Archicad Collaboration Guide",
        "category": "features"
      }
    }
  ],
  "total_results": 15,
  "query_time_ms": 145
}
```

#### POST /report
Generate report from query:

```bash
curl -X POST http://localhost:8002/report \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Compare BIM features across products",
    "style": "comparison"
  }'
```

## Project Structure

```
search/
├── src/
│   ├── mcp_server.py          # Main MCP server
│   ├── vector_search.py       # Vector search implementation
│   ├── vector_store.py        # Vector store management
│   ├── rephrase.py           # Query enhancement
│   ├── report.py             # Report generation
│   ├── search.py             # Main search orchestrator
│   ├── config.py             # Configuration management
│   ├── service_url_generator.py # URL utilities
│   └── justsearch.py         # Testing/demo script
├── Dockerfile                # Container build
├── requirements.txt          # Python dependencies
└── README.md                # Documentation
```

## Configuration

### Vector Store Configuration

```python
# src/config.py
VECTOR_CONFIG = {
    'embedding_model': 'models/embedding-001',
    'embedding_dimensions': 768,
    'similarity_function': 'cosine',
    'index_type': 'ivfflat',
    'index_lists': 100
}

SEARCH_CONFIG = {
    'max_results': 50,
    'default_threshold': 0.7,
    'query_expansion': True,
    'result_deduplication': True,
    'content_max_length': 2000
}
```

### LLM Configuration

```python
# Azure OpenAI settings
AZURE_CONFIG = {
    'deployment_name': 'gpt-4',
    'api_version': '2024-02-15-preview',
    'temperature': 0.1,
    'max_tokens': 2000,
    'timeout': 30
}

# Google AI settings
GOOGLE_CONFIG = {
    'model': 'models/embedding-001',
    'batch_size': 100,
    'retry_attempts': 3,
    'timeout': 15
}
```

## Data Ingestion

### Document Processing Pipeline

```python
# Example document ingestion
from src.vector_store import VectorStore

store = VectorStore()

# Process documents
documents = [
    {
        "content": "Archicad is a BIM software...",
        "metadata": {
            "source_url": "https://example.com/archicad",
            "title": "Archicad Overview",
            "category": "product-info",
            "vendor": "Graphisoft"
        }
    }
]

# Generate embeddings and store
await store.add_documents(documents)
```

### Bulk Import

```bash
# Import from JSON file
python scripts/import_documents.py --file data/documents.json

# Import from CSV
python scripts/import_documents.py --file data/products.csv --format csv

# Import from scraped data
python scripts/import_scraped_data.py --source mongodb://localhost:27017/scraped_data
```

### Data Format

Documents should follow this structure:

```json
{
  "documents": [
    {
      "content": "Main text content to be searched",
      "metadata": {
        "source_url": "https://example.com/page",
        "title": "Document Title",
        "category": "product-info|features|pricing|documentation",
        "vendor": "Graphisoft|Allplan|Vectorworks|etc",
        "product": "Archicad|Allplan Architecture|etc",
        "last_updated": "2024-01-01T00:00:00Z",
        "language": "en"
      }
    }
  ]
}
```

## Advanced Features

### Query Optimization

The service automatically optimizes queries:

```python
# Query enhancement pipeline
async def enhance_query(original_query: str) -> List[str]:
    """Generate optimized query variants."""
    
    # 1. Generate semantic variants
    variants = await rephraser.rephrase_query(original_query)
    
    # 2. Add domain-specific terms
    domain_variants = add_domain_terms(variants)
    
    # 3. Include related keywords
    keyword_variants = expand_with_keywords(domain_variants)
    
    return keyword_variants

# Search with multiple variants
results = []
for variant in await enhance_query(user_query):
    variant_results = await vector_search.search(variant)
    results.extend(variant_results)

# Deduplicate and rank
final_results = deduplicate_and_rank(results)
```

### Result Deduplication

Intelligent deduplication based on content similarity:

```python
def deduplicate_results(results: List[SearchResult]) -> List[SearchResult]:
    """Remove duplicate results based on content similarity."""
    
    unique_results = []
    seen_content = set()
    
    for result in sorted(results, key=lambda x: x.score, reverse=True):
        # Calculate content hash
        content_hash = hash_content(result.content)
        
        # Check for near-duplicates
        if not is_similar_content(content_hash, seen_content):
            unique_results.append(result)
            seen_content.add(content_hash)
    
    return unique_results
```

### Report Templates

Customizable report generation:

```python
# Report templates
REPORT_TEMPLATES = {
    'detailed': {
        'sections': ['overview', 'features', 'pricing', 'comparison'],
        'max_length': 2000,
        'include_quotes': True
    },
    'summary': {
        'sections': ['overview', 'key_points'],
        'max_length': 500,
        'bullet_points': True
    },
    'comparison': {
        'sections': ['feature_comparison', 'pricing_comparison'],
        'table_format': True,
        'highlight_differences': True
    }
}
```

## Performance Optimization

### Caching Strategy

```python
# Redis caching for search results
import redis
import json

class SearchCache:
    def __init__(self, redis_url: str):
        self.redis = redis.from_url(redis_url)
    
    def get_search_results(self, query_hash: str) -> Optional[List[dict]]:
        cached = self.redis.get(f"search:{query_hash}")
        return json.loads(cached) if cached else None
    
    def cache_search_results(self, query_hash: str, results: List[dict], ttl: int = 1800):
        self.redis.setex(f"search:{query_hash}", ttl, json.dumps(results))

# Usage
cache = SearchCache(REDIS_URL)
query_hash = hashlib.md5(query.encode()).hexdigest()

cached_results = cache.get_search_results(query_hash)
if not cached_results:
    results = await perform_search(query)
    cache.cache_search_results(query_hash, results)
```

### Database Optimization

```sql
-- Vector search optimization
CREATE INDEX CONCURRENTLY idx_documents_embedding 
ON documents USING ivfflat (embedding vector_cosine_ops);

-- Metadata filtering indexes
CREATE INDEX CONCURRENTLY idx_documents_category ON documents(category);
CREATE INDEX CONCURRENTLY idx_documents_vendor ON documents(vendor);
CREATE INDEX CONCURRENTLY idx_documents_updated ON documents(last_updated);

-- Full-text search support
CREATE INDEX CONCURRENTLY idx_documents_content_fts 
ON documents USING gin(to_tsvector('english', content));
```

## Monitoring and Debugging

### Metrics Collection

```python
# Performance metrics
from prometheus_client import Counter, Histogram

SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_DURATION = Histogram('search_duration_seconds', 'Search request duration')
EMBEDDING_DURATION = Histogram('embedding_duration_seconds', 'Embedding generation time')

@SEARCH_DURATION.time()
async def search_with_metrics(query: str):
    SEARCH_REQUESTS.inc()
    return await perform_search(query)
```

### Health Checks

```python
@app.route('/health')
async def health_check():
    """Comprehensive health check."""
    try:
        # Test database connection
        await vector_store.test_connection()
        
        # Test embedding service
        await embedding_service.test_embedding("test")
        
        # Test LLM service
        await llm_service.test_generation("test query")
        
        return {
            'status': 'healthy',
            'services': {
                'database': 'connected',
                'embeddings': 'available',
                'llm': 'available'
            }
        }
    except Exception as e:
        return {'status': 'unhealthy', 'error': str(e)}, 500
```

## Troubleshooting

### Common Issues

**Vector search returning no results**
- Check similarity threshold (lower values = more results)
- Verify embeddings are generated correctly
- Ensure database has indexed documents

**Slow search performance**
- Check vector index exists and is up to date
- Consider adjusting similarity threshold
- Monitor database query performance

**LLM API failures**
- Verify API keys are correct and active
- Check rate limiting and quotas
- Implement retry logic with backoff

**Memory issues with large result sets**
- Implement result pagination
- Adjust search limits
- Use streaming for large reports

### Performance Tuning

```python
# Optimize search parameters
OPTIMIZED_CONFIG = {
    'max_results_per_variant': 20,
    'similarity_threshold': 0.75,
    'query_variants': 2,
    'result_dedup_threshold': 0.9,
    'embedding_batch_size': 50
}

# Database connection pooling
DATABASE_POOL_CONFIG = {
    'min_size': 5,
    'max_size': 20,
    'command_timeout': 30,
    'server_settings': {
        'application_name': 'search_service',
        'jit': 'off'
    }
}
```
