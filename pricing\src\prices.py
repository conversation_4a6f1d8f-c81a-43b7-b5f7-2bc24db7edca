import json
import os

BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Load pricing data similar to providers.py pattern
def _load_pricing_data(filename):
    file_path = os.path.join(BASE_DIR, filename)
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

# Company data registry
COMPANIES = {
    "allplan": {"file": "allplan_pricing_data.json", "name": "Allplan"},
    "bluebeam": {"file": "bluebeam_pricing_data.json", "name": "Bluebeam"},
    "graphisoft": {"file": "graphisoft_pricing_data.json", "name": "Graphisoft"},
    "vectorworks": {"file": "vectorworks_pricing_data.json", "name": "Vectorworks"},
    "solibri": {"file": "solibri_pricing_data.json", "name": "Solibri"}
}

# Load all pricing data
pricing_data = {}
for company_key, company_info in COMPANIES.items():
    pricing_data[company_key] = _load_pricing_data(company_info["file"])


def _get_term_data(subscription_data, term):
    """Find term data in subscription terms array."""
    for term_info in subscription_data.get("terms", []):
        if term_info.get("term_id") == term:
            return term_info
    return None

def calculate_price(product_name: str, country_code: str, term: str, seats: int, company_name: str):
    """Calculate the total price for any supported company's subscription."""
    company_data = pricing_data[company_name.lower()]
    country_data = company_data[country_code]
    subscription_data = country_data["subscriptions"][product_name]
    term_data = _get_term_data(subscription_data, term)

    # This will raise AttributeError if term_data is None
    price_per_month = term_data["price_per_month"] if "price_per_month" in term_data else 0
    total_price_per_seat = term_data.get("total_price", price_per_month)
    total_price = total_price_per_seat * seats

    # Format commitment and billing for better readability
    commitment_data = term_data.get("commitment", {})
    billing_data = term_data.get("billing", {})

    commitment_str = f"{commitment_data.get('value', '')} {commitment_data.get('unit', '')}" if commitment_data else ""
    billing_str = f"{billing_data.get('cycle', '')} (${billing_data.get('price_per_cycle', 0)})" if billing_data else ""

    return {
        "product_name": product_name,
        "country_code": country_code,
        "country_name": country_data.get("country_name"),
        "term": term,
        "seats": seats,
        "price_per_month": price_per_month,
        "price_per_seat_total": total_price_per_seat,
        "total_price": total_price,
        "currency": term_data.get("currency"),
        "commitment": commitment_str.strip(),
        "billing": billing_str
    }

def get_countries(company_name: str):
    """Get list of available countries for any supported company."""
    return list(pricing_data[company_name.lower()].keys())

def get_term_options(company_name: str):
    """Get available term options for any supported company."""
    terms = set()
    company_data = pricing_data[company_name.lower()]
    for country_data in company_data.values():
        for subscription_data in country_data.get("subscriptions", {}).values():
            for term_info in subscription_data.get("terms", []):
                terms.add(term_info.get("term_id"))
    return list(terms)

def get_country_pricing(company_name: str, country_code: str):
    """Get pricing data for a specific country for any supported company."""
    return pricing_data[company_name.lower()][country_code]

def get_subscriptions(company_name: str, country_code: str):
    """Get available subscriptions for a specific country for any supported company."""
    country_data = pricing_data[company_name.lower()][country_code]
    return list(country_data.get("subscriptions", {}).keys())
