import asyncio


from config import (DOMAIN_LIST, CHUNK_SIZE_IN_CHARACTER, CHUNK_OVERLAP_SIZE_IN_CHARACTER, 
                    SAVE_BATCH_SIZE, GOOGLE_GEN_AI_EMBEDDING_MODEL, EMBEDDING_MODEL_DIMENSION, 
                    CONNECTION_STRING, DISTANCE_STRATEGY, MONGO_URL, MONGO_DATABASE_NAME)
from src.utils.chunking import ChunkingUtils
from src.utils.md_mongo_loader import MDMongoLoader
from src.utils.manage_table_create import manage_create_table
from src.utils.docs_convert_langchainDocument import ConvertDocsToLangchainDocumentsWithoutContext

from langchain_postgres import PGVectorStore
from langchain_postgres import PGEngine
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_postgres.v2.indexes import HNSWIndex

from sqlalchemy.ext.asyncio import create_async_engine

from tqdm import tqdm


async def main():

    mongo_loader = MDMongoLoader(mongo_url=MONGO_URL, database_name=MONGO_DATABASE_NAME)

    embedding_model = GoogleGenerativeAIEmbeddings(model=GOOGLE_GEN_AI_EMBEDDING_MODEL)

    engine = create_async_engine(
            CONNECTION_STRING,
        )
    
    pg_engine = PGEngine.from_engine(engine=engine)


    for domain in DOMAIN_LIST:
        collection_name = mongo_loader.get_latest_collection_name(prefix=domain)
        print(collection_name)
        if not collection_name:
            print(f"No collections found for domain: {domain}")

        print(f"Processing domain: {domain}, collection: {collection_name}")

        docs = mongo_loader.loadAllMarkDown_fromMongoDB(collectionName=collection_name)

        if not docs:
            print(f"No documents found in collection: {collection_name}")

        print(f"Loaded {len(docs)} documents from MongoDB for domain: {domain}")

        chunking_utils = ChunkingUtils(chunk_size=CHUNK_SIZE_IN_CHARACTER, chunk_overlap=CHUNK_OVERLAP_SIZE_IN_CHARACTER)
        chunking_utils.addChunkListsTo(docs)

        print(f"Chunked documents into {sum(len(doc.chunkList) for doc in docs)} chunks for domain: {domain}")

        TABLE_NAME = domain

        await manage_create_table(
            engine=engine,
            pg_engine=pg_engine,
            table_name=TABLE_NAME,
            vector_size=EMBEDDING_MODEL_DIMENSION,
            service_url_for_sync=CONNECTION_STRING.replace("postgresql+asyncpg", "postgresql")
        )

        STORE = await PGVectorStore.create(
            engine=pg_engine,
            table_name=TABLE_NAME,
            embedding_service=embedding_model,
            distance_strategy=DISTANCE_STRATEGY,
        )

        documents = ConvertDocsToLangchainDocumentsWithoutContext(documents=docs)

        num_batches = (len(documents) + SAVE_BATCH_SIZE - 1) // SAVE_BATCH_SIZE
        for i in tqdm(range(0, len(documents), SAVE_BATCH_SIZE), desc="Uploading batches", total=num_batches):
            batch = documents[i:i+SAVE_BATCH_SIZE]
            await STORE.aadd_documents(batch)

        index = HNSWIndex(name=f"{TABLE_NAME}_PGvector_index", distance_strategy=DISTANCE_STRATEGY)
        await STORE.aapply_vector_index(index)

if __name__ == "__main__":
    asyncio.run(main())