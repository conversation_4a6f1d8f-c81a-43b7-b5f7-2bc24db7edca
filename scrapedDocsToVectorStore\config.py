import os
from src.utils.service_url_generator import get_vector_store_service_url
from langchain_postgres.v2.indexes import DistanceStrategy

MONGO_URL=os.getenv("MONGO_URL") 
MONGO_DATABASE_NAME=os.getenv("MONGO_DATABASE_NAME")

DOMAIN_LIST = os.getenv("DOMAINS_LIST", "").split(",")

CHUNK_SIZE_IN_CHARACTER = 2000
CHUNK_OVERLAP_SIZE_IN_CHARACTER = 400

SAVE_BATCH_SIZE = 100

GOOGLE_GEN_AI_EMBEDDING_MODEL = "models/embedding-001"
EMBEDDING_MODEL_DIMENSION = 768

CONNECTION_STRING = get_vector_store_service_url()

DISTANCE_STRATEGY = DistanceStrategy.EUCLIDEAN