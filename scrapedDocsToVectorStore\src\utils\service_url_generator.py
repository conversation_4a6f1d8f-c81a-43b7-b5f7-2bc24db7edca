import os

def get_vector_store_service_url():
    """
    Constructs the service URL for the Timescale database using environment variables.
    
    Returns:
        str: The service URL for the Timescale database.
    """
    POSTGRES_USER = os.getenv("POSTGRES_USER")
    POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD")
    POSTGRES_HOST = os.getenv("POSTGRES_HOST")
    POSTGRES_PORT = os.getenv("POSTGRES_PORT")
    POSTGRES_NAME = os.getenv("POSTGRES_DB")

    SERVICE_URL = f"postgresql+asyncpg://{POSTGRES_USER}:{POSTGRES_PASSWORD}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_NAME}"
    return SERVICE_URL