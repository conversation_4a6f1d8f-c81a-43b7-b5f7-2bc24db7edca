from scrapy.spiders import <PERSON>raw<PERSON><PERSON>pid<PERSON>, Rule
from scrapy.linkextractors import LinkExtractor
import json
import markdownify
import tldextract

class MarkdownSpider(CrawlSpider):
    name = "markdown_spider"
    rules = (
        Rule(LinkExtractor(), callback='parse_item', follow=True),
    )
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
           "graphibot.middlewares.IgnoreSubdomainDownloadMiddleware": 543,
           "graphibot.middlewares.IgnoreCountryCodeDownloadMiddleware": 544,
           "graphibot.middlewares.IgnoreNonHtmlDownloadMiddleware": 545,
        },
        "ITEM_PIPELINES": {
            "graphibot.pipelines.MongoDBPipeline": 100,
        },
        "DEPTH_PRIORITY": 1,
        "SCHEDULER_DISK_QUEUE": "scrapy.squeues.PickleFifoDiskQueue",
        "SCHEDULER_MEMORY_QUEUE": "scrapy.squeues.FifoMemoryQueue",
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Get the config file path from the spider arguments (-a)
        config_path = kwargs.get('config_path')
        if not config_path:
            raise ValueError("A config_path argument must be provided. e.g., -a config_path=path/to/config.json")

        self.logger.info(f"Loading configuration from: {config_path}")
        
        # Load the configuration from the JSON file
        with open(config_path) as config_file:
            config = json.load(config_file)

        # Dynamically set the spider's attributes from the loaded config
        self.start_urls = config.get('start_urls', [])
        self.allowed_domains = config.get('allowed_domains', [])

        self.logger.info(f"Spider configured for {self.allowed_domains} with {len(self.rules)} rule(s).")

    def parse_item(self, response):
        try:
            # Convert the response text to Markdown
            markdown = markdownify.markdownify(response.text)
            self.logger.info(f"Successfully converted {response.url} to Markdown.")

            # Extract the domain from the URL
            domain = tldextract.extract(response.url).domain
            
            # Yield processed item
            yield {
                "domain": domain,
                "url": response.url,
                "status": response.status,
                "text": response.text,
                "md": markdown,
            }
        except Exception as e:
            message = f"Skipping item {response.url} due to error: {e}"
            self.logger.warning(message)
            return
