$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"



Write-Host "`n--- Applying Kubernetes Job Configurations ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\jobs")

    Write-Host "Creating configmap of sites to scrape..."
    kubectl create configmap scrapy-config --from-file=scrapy-config.json
    Write-Host "Scrapy config created."

    Write-Host "Running scraping job..."
    kubectl apply -f scraped-websites-scrapy-job.yaml
    Write-Host "Scraped websites job applied. Check completion with: kubectl get pods"

    Write-Host "Running vectorizer job..."
    kubectl apply -f mcp-search-vectorizer-pipeline-job.yaml
    Write-Host "Vectorizer pipeline job applied. Check completion with: kubectl get pods"

    Pop-Location
    Write-Host "All Kubernetes job configurations applied successfully."
    Write-Host "Use 'kubectl get pods' to check job completion status."
}
catch {
    Write-Error "Error applying job configurations: $($_.Exception.Message)"
    exit 1
}
