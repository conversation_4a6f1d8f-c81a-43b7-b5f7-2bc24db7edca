from fastmcp import FastMCP, Context
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from langchain_core.documents import Document

from typing import List

from vector_search import SearchService

# Create an MCP server
mcp = FastMCP("RAG search service")

@dataclass
class AppContext:
    vector_search: SearchService

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    vector_search = SearchService()
    await vector_search.initialize()
    try:
        yield AppContext(vector_search=vector_search)
    finally:
        pass

# Pass lifespan to server
mcp = FastMCP("RAG search service", lifespan=app_lifespan)

@mcp.tool()
def list_companies(
    ctx: Context,
):
    """
    This tool is used for listing the companies that are available in the RAG database.
    
    Returns:
        A list of company names that are available in the RAG database.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    results=vector_search.get_available_companies()
    return results


@mcp.tool()
async def search_for_context(
    ctx: Context,
    company: str,
    query: str,
) -> List[Document]:
    """
    This tool is used for searching and gathering information from the RAG database mainly about Nemetschek and its products.

    Args:
        company: The company name which the query is about.
        query: Based on the given query, the tool will search for relevant information in the database with vectorsearch retriever.    
    Returns:
        A list of chunks which come from webpages and transformed into markdown format and the sources of the webpages where chunks come from.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    company=company.lower()

    if company not in vector_search.get_available_companies():
        raise ValueError(f"Company '{company}' is not available. Available companies are: {', '.join(vector_search.get_available_companies())}")

    # Perform a similarity search using all the rephrased queries
    results = await vector_search.search_query(domain=company, query=query, k=20)

    return results


if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)