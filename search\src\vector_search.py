from config import (
    DOMAIN_LIST, GOOGLE_GEN_AI_EMBEDDING_MODEL,  
    CONNECTION_STRING, DISTANCE_STRATEGY, RESPONSE_CHUNK_NUM
)

from langchain_postgres import PGVectorStore
from langchain_postgres import PGEngine
from sqlalchemy.ext.asyncio import create_async_engine
from langchain_google_genai import GoogleGenerativeAIEmbeddings

class SearchService:
    def __init__(self):
        self.connection_string = CONNECTION_STRING

    async def initialize(self):
        engine = create_async_engine(self.connection_string)
        self.pg_engine = PGEngine.from_engine(engine)
        self.embedding_model = GoogleGenerativeAIEmbeddings(model=GOOGLE_GEN_AI_EMBEDDING_MODEL)

    async def get_vector_store(self, domain: str):
            store = await PGVectorStore.create(
                engine=self.pg_engine,
                table_name=domain,
                embedding_service=self.embedding_model,
                distance_strategy=DISTANCE_STRATEGY
            )
            return store

    def get_available_companies(self):
        return [d.strip().lower() for d in DOMAIN_LIST if d.strip()]

    async def search_query(self, domain: str, query: str, k: int = RESPONSE_CHUNK_NUM):
        store = await self.get_vector_store(domain)
        return await store.asimilarity_search(query=query, k=k)

#TODO: It have to be refactored because the filter not working because langchain save source in postgres like JSON metadata.
    async def search_chunks_by_source(self, domain: str, source: str):
        store = await self.get_vector_store(domain)
        results = await store.asimilarity_search(query="", k=4000)
        return [doc for doc in results if doc.metadata.get("source") == source]
    

ss=SearchService()
print(ss.connection_string)
